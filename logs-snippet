Progress update: 85% (1/1, step 5)
host_dashboard.js:5745 Progress update: 95% (1/1, step 6)
host_dashboard.js:91 Loading properties for host from session user
host_dashboard.js:122 API Response: {properties: Array(2), success: true}
host_dashboard.js:124 Properties from API: (2) [{…}, {…}]
host_dashboard.js:170 Creating property card for: 223f3bd6-a26d-445f-85d2-f7472a6a9e58
host_dashboard.js:171 Property data: {address: 'Watertown, Massachusetts, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Tue, 22 Jul 2025 00:42:21 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
host_dashboard.js:170 Creating property card for: f369ea47-421a-45fa-8528-9a4815dfad49
host_dashboard.js:171 Property data: {address: 'Watertown, Massachusetts, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Mon, 21 Jul 2025 21:22:00 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
host_dashboard.js:91 Loading properties for host from session user
host_dashboard.js:122 API Response: {properties: Array(2), success: true}
host_dashboard.js:124 Properties from API: (2) [{…}, {…}]
host_dashboard.js:170 Creating property card for: 223f3bd6-a26d-445f-85d2-f7472a6a9e58
host_dashboard.js:171 Property data: {address: 'Watertown, Massachusetts, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Tue, 22 Jul 2025 00:42:21 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
host_dashboard.js:170 Creating property card for: f369ea47-421a-45fa-8528-9a4815dfad49
host_dashboard.js:171 Property data: {address: 'Watertown, Massachusetts, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Mon, 21 Jul 2025 21:22:00 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
host_dashboard.js:1169 Starting property setup for: 223f3bd6-a26d-445f-85d2-f7472a6a9e58
host_dashboard.js:1182 Fetched latest property data for setup: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:40 Opening Property Setup Modal
property-setup-modal.js:41 Property ID: 223f3bd6-a26d-445f-85d2-f7472a6a9e58
property-setup-modal.js:42 Property Data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:43 Property amenities: {appliances: Array(9), basic: Array(40)}
property-setup-modal.js:158 Loaded setup progress from property data: {houseRules: 0, emergencyInfo: 0, propertyFacts: 0}
property-setup-modal.js:785 Normalized "Refrigerator" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Microwave" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Freezer" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Stove" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Oven" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Coffee maker" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Toaster" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Blender" location to Kitchen (was: "kitchen")
property-setup-modal.js:803 Enhanced categorization complete: {basicCount: 40, appliancesCount: 9, movedItems: 0}
property-setup-modal.js:343 Loading amenities section
property-setup-modal.js:344 Property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:345 Enhanced amenities data: {basic: Array(40), appliances: Array(9)}
property-setup-modal.js:346 Basic amenities count: 40
property-setup-modal.js:347 Appliances count: 9
property-setup-modal.js:1024 Step indicator element: <div id=​"step-indicator" class=​"text-sm text-gray-600">​Step 1 of 5: Basic Information​</div>​
property-setup-modal.js:1025 Current step: 1 Step name: Basic Information
property-setup-modal.js:1028 Updating step indicator to: Step 1 of 5: Basic Information
property-setup-modal.js:488 Validation errors found: ['WiFi Network Name and Password are required']
property-setup-modal.js:609 Scrolled setup-step-content to top
property-setup-modal.js:488 Validation errors found: ['WiFi Network Name and Password are required']
property-setup-modal.js:609 Scrolled setup-step-content to top
property-setup-modal.js:819 🔍 Collecting current amenities...
property-setup-modal.js:823 Basic amenities container: <div class=​"grid grid-cols-1 md:​grid-cols-2 gap-2" id=​"basic-amenities">​…​</div>​grid
property-setup-modal.js:827 Found 40 basic amenity elements
property-setup-modal.js:833 Amenity 0: "Lock on bedroom door" - Checked: true
property-setup-modal.js:833 Amenity 1: "Kitchen" - Checked: true
property-setup-modal.js:833 Amenity 2: "Wifi" - Checked: true
property-setup-modal.js:833 Amenity 3: "Free parking on premises" - Checked: true
property-setup-modal.js:833 Amenity 4: "Air conditioning" - Checked: true
property-setup-modal.js:833 Amenity 5: "Bathtub" - Checked: true
property-setup-modal.js:833 Amenity 6: "Shared patio or balcony" - Checked: true
property-setup-modal.js:833 Amenity 7: "Shared backyard – Fully fenced" - Checked: true
property-setup-modal.js:833 Amenity 8: "Cleaning products" - Checked: true
property-setup-modal.js:833 Amenity 9: "Jason body soap" - Checked: true
property-setup-modal.js:833 Amenity 10: "Hot water" - Checked: true
property-setup-modal.js:833 Amenity 11: "Essentials" - Checked: true
property-setup-modal.js:833 Amenity 12: "Hangers" - Checked: true
property-setup-modal.js:833 Amenity 13: "Bed linens" - Checked: true
property-setup-modal.js:833 Amenity 14: "Extra pillows and blankets" - Checked: true
property-setup-modal.js:833 Amenity 15: "Room-darkening shades" - Checked: true
property-setup-modal.js:833 Amenity 16: "Iron" - Checked: true
property-setup-modal.js:833 Amenity 17: "Safe" - Checked: true
property-setup-modal.js:833 Amenity 18: "Clothing storage: closet and dresser" - Checked: true
property-setup-modal.js:833 Amenity 19: "Game console: PS4" - Checked: true
property-setup-modal.js:833 Amenity 20: "Books and reading material" - Checked: true
property-setup-modal.js:833 Amenity 21: "Portable fans" - Checked: true
property-setup-modal.js:833 Amenity 22: "Heating" - Checked: true
property-setup-modal.js:833 Amenity 23: "Smoke alarm" - Checked: true
property-setup-modal.js:833 Amenity 24: "Carbon monoxide alarm" - Checked: true
property-setup-modal.js:833 Amenity 25: "Fire extinguisher" - Checked: true
property-setup-modal.js:833 Amenity 26: "Cooking basics" - Checked: true
property-setup-modal.js:833 Amenity 27: "Dishes and silverware" - Checked: true
property-setup-modal.js:833 Amenity 28: "Wine glasses" - Checked: true
property-setup-modal.js:833 Amenity 29: "Baking sheet" - Checked: true
property-setup-modal.js:833 Amenity 30: "Barbecue utensils" - Checked: true
property-setup-modal.js:833 Amenity 31: "Dining table" - Checked: true
property-setup-modal.js:833 Amenity 32: "Laundromat nearby" - Checked: true
property-setup-modal.js:833 Amenity 33: "Outdoor furniture" - Checked: true
property-setup-modal.js:833 Amenity 34: "Outdoor dining area" - Checked: true
property-setup-modal.js:833 Amenity 35: "BBQ grill" - Checked: true
property-setup-modal.js:833 Amenity 36: "Free street parking" - Checked: true
property-setup-modal.js:833 Amenity 37: "Private living room" - Checked: true
property-setup-modal.js:833 Amenity 38: "Self check-in" - Checked: true
property-setup-modal.js:833 Amenity 39: "Keypad" - Checked: true
property-setup-modal.js:845 Appliances container: <div class=​"space-y-2" id=​"appliances-list">​…​</div>​
property-setup-modal.js:849 Found 9 appliance elements
property-setup-modal.js:853 Appliance 0: Found 4 input fields
property-setup-modal.js:861 Appliance 0 data: Name="TV", Location="Living Room", Brand="", Model=""
property-setup-modal.js:853 Appliance 1: Found 4 input fields
property-setup-modal.js:861 Appliance 1 data: Name="Refrigerator", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 2: Found 4 input fields
property-setup-modal.js:861 Appliance 2 data: Name="Microwave", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 3: Found 4 input fields
property-setup-modal.js:861 Appliance 3 data: Name="Freezer", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 4: Found 4 input fields
property-setup-modal.js:861 Appliance 4 data: Name="Stove", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 5: Found 4 input fields
property-setup-modal.js:861 Appliance 5 data: Name="Oven", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 6: Found 4 input fields
property-setup-modal.js:861 Appliance 6 data: Name="Coffee maker", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 7: Found 4 input fields
property-setup-modal.js:861 Appliance 7 data: Name="Toaster", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 8: Found 4 input fields
property-setup-modal.js:861 Appliance 8 data: Name="Blender", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:877 Collected current amenities: {basic: Array(40), appliances: Array(9)}
property-setup-modal.js:513 Saving basic information with amenities: {name: 'Warm, private room near Harvard and Boston', address: 'Watertown, Massachusetts, United States', description: 'HomesHomesNEWNEWExperiencesExperiences, newNEWNEWS…oved homes on Airbnb, according to guestsRated 4.', icalUrl: 'https://www.airbnb.com/calendar/ical/1376252243023110567.ics?s=afd260cb47e4a62730f56c51a3fd1db1', checkInTime: '15:00', …}
property-setup-modal.js:819 🔍 Collecting current amenities...
property-setup-modal.js:823 Basic amenities container: <div class=​"grid grid-cols-1 md:​grid-cols-2 gap-2" id=​"basic-amenities">​…​</div>​grid
property-setup-modal.js:827 Found 40 basic amenity elements
property-setup-modal.js:833 Amenity 0: "Lock on bedroom door" - Checked: true
property-setup-modal.js:833 Amenity 1: "Kitchen" - Checked: true
property-setup-modal.js:833 Amenity 2: "Wifi" - Checked: true
property-setup-modal.js:833 Amenity 3: "Free parking on premises" - Checked: true
property-setup-modal.js:833 Amenity 4: "Air conditioning" - Checked: true
property-setup-modal.js:833 Amenity 5: "Bathtub" - Checked: true
property-setup-modal.js:833 Amenity 6: "Shared patio or balcony" - Checked: true
property-setup-modal.js:833 Amenity 7: "Shared backyard – Fully fenced" - Checked: true
property-setup-modal.js:833 Amenity 8: "Cleaning products" - Checked: true
property-setup-modal.js:833 Amenity 9: "Jason body soap" - Checked: true
property-setup-modal.js:833 Amenity 10: "Hot water" - Checked: true
property-setup-modal.js:833 Amenity 11: "Essentials" - Checked: true
property-setup-modal.js:833 Amenity 12: "Hangers" - Checked: true
property-setup-modal.js:833 Amenity 13: "Bed linens" - Checked: true
property-setup-modal.js:833 Amenity 14: "Extra pillows and blankets" - Checked: true
property-setup-modal.js:833 Amenity 15: "Room-darkening shades" - Checked: true
property-setup-modal.js:833 Amenity 16: "Iron" - Checked: true
property-setup-modal.js:833 Amenity 17: "Safe" - Checked: true
property-setup-modal.js:833 Amenity 18: "Clothing storage: closet and dresser" - Checked: true
property-setup-modal.js:833 Amenity 19: "Game console: PS4" - Checked: true
property-setup-modal.js:833 Amenity 20: "Books and reading material" - Checked: true
property-setup-modal.js:833 Amenity 21: "Portable fans" - Checked: true
property-setup-modal.js:833 Amenity 22: "Heating" - Checked: true
property-setup-modal.js:833 Amenity 23: "Smoke alarm" - Checked: true
property-setup-modal.js:833 Amenity 24: "Carbon monoxide alarm" - Checked: true
property-setup-modal.js:833 Amenity 25: "Fire extinguisher" - Checked: true
property-setup-modal.js:833 Amenity 26: "Cooking basics" - Checked: true
property-setup-modal.js:833 Amenity 27: "Dishes and silverware" - Checked: true
property-setup-modal.js:833 Amenity 28: "Wine glasses" - Checked: true
property-setup-modal.js:833 Amenity 29: "Baking sheet" - Checked: true
property-setup-modal.js:833 Amenity 30: "Barbecue utensils" - Checked: true
property-setup-modal.js:833 Amenity 31: "Dining table" - Checked: true
property-setup-modal.js:833 Amenity 32: "Laundromat nearby" - Checked: true
property-setup-modal.js:833 Amenity 33: "Outdoor furniture" - Checked: true
property-setup-modal.js:833 Amenity 34: "Outdoor dining area" - Checked: true
property-setup-modal.js:833 Amenity 35: "BBQ grill" - Checked: true
property-setup-modal.js:833 Amenity 36: "Free street parking" - Checked: true
property-setup-modal.js:833 Amenity 37: "Private living room" - Checked: true
property-setup-modal.js:833 Amenity 38: "Self check-in" - Checked: true
property-setup-modal.js:833 Amenity 39: "Keypad" - Checked: true
property-setup-modal.js:845 Appliances container: <div class=​"space-y-2" id=​"appliances-list">​…​</div>​
property-setup-modal.js:849 Found 9 appliance elements
property-setup-modal.js:853 Appliance 0: Found 4 input fields
property-setup-modal.js:861 Appliance 0 data: Name="TV", Location="Living Room", Brand="", Model=""
property-setup-modal.js:853 Appliance 1: Found 4 input fields
property-setup-modal.js:861 Appliance 1 data: Name="Refrigerator", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 2: Found 4 input fields
property-setup-modal.js:861 Appliance 2 data: Name="Microwave", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 3: Found 4 input fields
property-setup-modal.js:861 Appliance 3 data: Name="Freezer", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 4: Found 4 input fields
property-setup-modal.js:861 Appliance 4 data: Name="Stove", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 5: Found 4 input fields
property-setup-modal.js:861 Appliance 5 data: Name="Oven", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 6: Found 4 input fields
property-setup-modal.js:861 Appliance 6 data: Name="Coffee maker", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 7: Found 4 input fields
property-setup-modal.js:861 Appliance 7 data: Name="Toaster", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 8: Found 4 input fields
property-setup-modal.js:861 Appliance 8 data: Name="Blender", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:877 Collected current amenities: {basic: Array(40), appliances: Array(9)}
property-setup-modal.js:513 Saving basic information with amenities: {name: 'Warm, private room near Harvard and Boston', address: 'Watertown, Massachusetts, United States', description: 'HomesHomesNEWNEWExperiencesExperiences, newNEWNEWS…oved homes on Airbnb, according to guestsRated 4.', icalUrl: 'https://www.airbnb.com/calendar/ical/1376252243023110567.ics?s=afd260cb47e4a62730f56c51a3fd1db1', checkInTime: '15:00', …}
property-setup-modal.js:897 Step 1 saved successfully
property-setup-modal.js:926 Updated amenities in local property data: {basic: Array(40), appliances: Array(9)}
property-setup-modal.js:948 Updated local property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:897 Step 1 saved successfully
property-setup-modal.js:926 Updated amenities in local property data: {basic: Array(40), appliances: Array(9)}
property-setup-modal.js:948 Updated local property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:2569 🔧 Loading unified rules section...
property-setup-modal.js:2608 🔧 Loading original unified rules from import data...
property-setup-modal.js:2723 🔗 Found 2 "Before you leave" rules, concatenating...
property-setup-modal.js:2753 ✅ Created concatenated "Before you leave" rule: Before you leave, clean up, lock up, clean up, lock up.
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "5 guests maximum"
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "Quiet hours: 9:00 PM - 7:00 AM"
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "No parties or events"
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "No smoking"
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "No pets"
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "I am vaccinated. Please no late arrivals past 10pm. Quiet hours from 11pm to 8am. Patio is available from May to October. Street parking is available from end of March to end of November. Guests staying during winter months will have to park in the driveway. It is shared with upstairs neighbors during that time. We can easily coordinate cars."
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "2 guests maximum"
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "Check-in: 3:00 PM - 10:00 PM"
property-setup-modal.js:2783 📋 Deduplicated rules: 17 → 9
property-setup-modal.js:2621 🔍 Debugging imported rules:
property-setup-modal.js:2622   - extractedRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:2623   - deepExtractedRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:2624   - allImportedRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:2625   - propertyData.importData: {extractedAt: '2025-07-21T19:42:21.696442', rawData: {…}, source: 'airbnb'}
property-setup-modal.js:2626   - propertyData.importData?.rawData: {extracted: {…}, house_rules: Array(9), listing: {…}}
property-setup-modal.js:2669 Filtered to 9 valid imported rules
property-setup-modal.js:2872 🚫 Conflict detected: Default rule "Smoking" conflicts with imported rule "No smoking"
property-setup-modal.js:2872 🚫 Conflict detected: Default rule "Parties and Events" conflicts with imported rule "No parties or events"
property-setup-modal.js:2872 🚫 Conflict detected: Default rule "Quiet Hours" conflicts with imported rule "Quiet hours: 9:00 PM - 7:00 AM"
property-setup-modal.js:2872 🚫 Conflict detected: Default rule "Pets" conflicts with imported rule "No pets"
property-setup-modal.js:2872 🚫 Conflict detected: Default rule "Property Capacity" conflicts with imported rule "5 guests maximum"
property-setup-modal.js:2675 Using 2 default rules (5 filtered out due to conflicts)
property-setup-modal.js:2683 Total unified rules: 11
property-setup-modal.js:2980 ✅ Rendered 11 unified rules (9 imported, 2 default)
property-setup-modal.js:1024 Step indicator element: <div id=​"step-indicator" class=​"text-sm text-gray-600">​Step 2 of 5: House Rules​</div>​
property-setup-modal.js:1025 Current step: 2 Step name: House Rules
property-setup-modal.js:1028 Updating step indicator to: Step 2 of 5: House Rules
property-setup-modal.js:3199 Uncaught TypeError: Cannot read properties of null (reading 'checked')
    at PropertySetupModal.toggleRule (property-setup-modal.js:3199:36)
    at HTMLInputElement.onchange (dashboard:1:20)
toggleRule @ property-setup-modal.js:3199
onchange @ dashboard:1Understand this error
property-setup-modal.js:2991 House rule checkbox 5 changed to: false
property-setup-modal.js:3010 Updated house rule 5 enabled status: false
property-setup-modal.js:3199 Uncaught TypeError: Cannot read properties of null (reading 'checked')
    at PropertySetupModal.toggleRule (property-setup-modal.js:3199:36)
    at HTMLInputElement.onchange (dashboard:1:20)
toggleRule @ property-setup-modal.js:3199
onchange @ dashboard:1Understand this error
property-setup-modal.js:2991 House rule checkbox 5 changed to: true
property-setup-modal.js:3010 Updated house rule 5 enabled status: true
property-setup-modal.js:3018 Auto-saving house rules...
property-setup-modal.js:3051 Updated current house rules from form: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:897 Step 2 saved successfully
property-setup-modal.js:932 Updated house rules in local property data: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:948 Updated local property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:3001 House rule text changed: rule_content_7
property-setup-modal.js:3018 Auto-saving house rules...
property-setup-modal.js:3051 Updated current house rules from form: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:3001 House rule text changed: rule_content_7
property-setup-modal.js:897 Step 2 saved successfully
property-setup-modal.js:932 Updated house rules in local property data: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:948 Updated local property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}address: "Watertown, Massachusetts, United States"airbnbListingUrl: "https://www.airbnb.com/rooms/18839204"amenities: {basic: Array(40), appliances: Array(9)}checkInTime: "15:00"checkOutTime: "11:00"createdAt: "Tue, 22 Jul 2025 00:42:21 GMT"description: "HomesHomesNEWNEWExperiencesExperiences, newNEWNEWServicesServices, newStart your searchLocationAnywhereCheck in / Check outAny weekGuestsAdd guestsBecome a hostWarm, private room near Harvard and BostonShareSaveShow all photosPhotosAmenitiesReviewsLocationAdd dates for pricesAdd dates for prices4. 85 ·246 reviewsRated 4. 85 out of 5 from 246 reviews. Check availabilityRoom in Watertown, Massachusetts1 queen bed··Shared bathroomGuest favoriteOne of the most loved homes on Airbnb, according to guestsRated 4."hostId: "xmM4At4jH3eeNfq69WpBqPyMg952"houseRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]icalUrl: "https://www.airbnb.com/calendar/ical/1376252243023110567.ics?s=afd260cb47e4a62730f56c51a3fd1db1"id: "223f3bd6-a26d-445f-85d2-f7472a6a9e58"importData: {extractedAt: '2025-07-21T19:42:21.696442', rawData: {…}, source: 'airbnb'}name: "Warm, private room near Harvard and Boston"new: truesetupProgress: {step1_basic: false, step2_rules: false, step3_emergency: false, step4_facts: false, step5_review: false}status: "inactive"updatedAt: "Tue, 22 Jul 2025 00:42:21 GMT"wifiDetails: {networkName: 'welcome123', password: '123welcome#'}[[Prototype]]: Object
property-setup-modal.js:3018 Auto-saving house rules...
property-setup-modal.js:3051 Updated current house rules from form: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]0: {description: '5 guests maximum', type: 'rule', enabled: true, source: 'imported', content: '5 guests maximum'}1: {description: 'Quiet hours: 9:00 PM - 7:00 AM', type: 'rule', enabled: true, source: 'imported', content: 'Quiet hours: 9:00 PM - 7:00 AM'}2: {description: 'No parties or events', type: 'rule', enabled: true, source: 'imported', content: 'No parties or events'}3: {description: 'No smoking', type: 'rule', enabled: true, source: 'imported', content: 'No smoking'}4: {description: 'No pets', type: 'rule', enabled: true, source: 'imported', content: 'No pets'}5: {description: 'I am vaccinated. Please no late arrivals past 10pm… during that time. We can easily coordinate cars.', enabled: true, source: 'imported', title: 'Quiet hours (10pm - 11pm)', type: 'rule', …}6: {description: '2 guests maximum', enabled: true, source: 'imported', title: 'Maximum 2 guests', type: 'rule', …}7: {description: 'Check-in: 3:00 PM - 10:00 PM', enabled: true, source: 'imported', title: 'Check-in', type: 'rule', …}8: {id: 'before_you_leave_combined', title: 'Before you leave', description: 'Before you leave, clean up, lock up, clean up, lock up.', content: 'Before you leave, clean up, lock up, clean up, lock up.', type: 'imported', …}9: {id: 'commercial_photography', title: 'Commercial Photography', content: 'No commercial photography or filming without prior approval', enabled: false, type: 'default', …}10: {id: 'shoes', title: 'Shoes in Property', content: 'Remove shoes when entering the property', enabled: false, type: 'default', …}length: 11[[Prototype]]: Array(0)
property-setup-modal.js:897 Step 2 saved successfully
property-setup-modal.js:932 Updated house rules in local property data: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]0: {description: '5 guests maximum', type: 'rule', enabled: true, source: 'imported', content: '5 guests maximum'}1: {description: 'Quiet hours: 9:00 PM - 7:00 AM', type: 'rule', enabled: true, source: 'imported', content: 'Quiet hours: 9:00 PM - 7:00 AM'}2: {description: 'No parties or events', type: 'rule', enabled: true, source: 'imported', content: 'No parties or events'}3: {description: 'No smoking', type: 'rule', enabled: true, source: 'imported', content: 'No smoking'}4: {description: 'No pets', type: 'rule', enabled: true, source: 'imported', content: 'No pets'}5: {description: 'I am vaccinated. Please no late arrivals past 10pm… during that time. We can easily coordinate cars.', enabled: true, source: 'imported', title: 'Quiet hours (10pm - 11pm)', type: 'rule', …}6: {description: '2 guests maximum', enabled: true, source: 'imported', title: 'Maximum 2 guests', type: 'rule', …}7: {description: 'Check-in: 3:00 PM - 10:00 PM', enabled: true, source: 'imported', title: 'Check-in', type: 'rule', …}8: {id: 'before_you_leave_combined', title: 'Before you leave', description: 'Before you leave, clean up, lock up, clean up, lock up.', content: 'Before you leave, clean up, lock up, clean up, lock up.', type: 'imported', …}length: 9[[Prototype]]: Array(0)
property-setup-modal.js:948 Updated local property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
host_dashboard.js:1169 Starting property setup for: 223f3bd6-a26d-445f-85d2-f7472a6a9e58
host_dashboard.js:1182 Fetched latest property data for setup: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:40 Opening Property Setup Modal
property-setup-modal.js:41 Property ID: 223f3bd6-a26d-445f-85d2-f7472a6a9e58
property-setup-modal.js:42 Property Data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:43 Property amenities: {appliances: Array(9), basic: Array(40)}
property-setup-modal.js:158 Loaded setup progress from property data: {houseRules: 9, emergencyInfo: 0, propertyFacts: 0}
property-setup-modal.js:785 Normalized "Refrigerator" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Microwave" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Freezer" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Stove" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Oven" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Coffee maker" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Toaster" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Blender" location to Kitchen (was: "kitchen")
property-setup-modal.js:803 Enhanced categorization complete: {basicCount: 40, appliancesCount: 9, movedItems: 0}
property-setup-modal.js:343 Loading amenities section
property-setup-modal.js:344 Property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:345 Enhanced amenities data: {basic: Array(40), appliances: Array(9)}
property-setup-modal.js:346 Basic amenities count: 40
property-setup-modal.js:347 Appliances count: 9
property-setup-modal.js:1024 Step indicator element: <div id=​"step-indicator" class=​"text-sm text-gray-600">​Step 1 of 5: Basic Information​</div>​
property-setup-modal.js:1025 Current step: 1 Step name: Basic Information
property-setup-modal.js:1028 Updating step indicator to: Step 1 of 5: Basic Information
property-setup-modal.js:819 🔍 Collecting current amenities...
property-setup-modal.js:823 Basic amenities container: <div class=​"grid grid-cols-1 md:​grid-cols-2 gap-2" id=​"basic-amenities">​…​</div>​grid
property-setup-modal.js:827 Found 40 basic amenity elements
property-setup-modal.js:833 Amenity 0: "Lock on bedroom door" - Checked: true
property-setup-modal.js:833 Amenity 1: "Kitchen" - Checked: true
property-setup-modal.js:833 Amenity 2: "Wifi" - Checked: true
property-setup-modal.js:833 Amenity 3: "Free parking on premises" - Checked: true
property-setup-modal.js:833 Amenity 4: "Air conditioning" - Checked: true
property-setup-modal.js:833 Amenity 5: "Bathtub" - Checked: true
property-setup-modal.js:833 Amenity 6: "Shared patio or balcony" - Checked: true
property-setup-modal.js:833 Amenity 7: "Shared backyard – Fully fenced" - Checked: true
property-setup-modal.js:833 Amenity 8: "Cleaning products" - Checked: true
property-setup-modal.js:833 Amenity 9: "Jason body soap" - Checked: true
property-setup-modal.js:833 Amenity 10: "Hot water" - Checked: true
property-setup-modal.js:833 Amenity 11: "Essentials" - Checked: true
property-setup-modal.js:833 Amenity 12: "Hangers" - Checked: true
property-setup-modal.js:833 Amenity 13: "Bed linens" - Checked: true
property-setup-modal.js:833 Amenity 14: "Extra pillows and blankets" - Checked: true
property-setup-modal.js:833 Amenity 15: "Room-darkening shades" - Checked: true
property-setup-modal.js:833 Amenity 16: "Iron" - Checked: true
property-setup-modal.js:833 Amenity 17: "Safe" - Checked: true
property-setup-modal.js:833 Amenity 18: "Clothing storage: closet and dresser" - Checked: true
property-setup-modal.js:833 Amenity 19: "Game console: PS4" - Checked: true
property-setup-modal.js:833 Amenity 20: "Books and reading material" - Checked: true
property-setup-modal.js:833 Amenity 21: "Portable fans" - Checked: true
property-setup-modal.js:833 Amenity 22: "Heating" - Checked: true
property-setup-modal.js:833 Amenity 23: "Smoke alarm" - Checked: true
property-setup-modal.js:833 Amenity 24: "Carbon monoxide alarm" - Checked: true
property-setup-modal.js:833 Amenity 25: "Fire extinguisher" - Checked: true
property-setup-modal.js:833 Amenity 26: "Cooking basics" - Checked: true
property-setup-modal.js:833 Amenity 27: "Dishes and silverware" - Checked: true
property-setup-modal.js:833 Amenity 28: "Wine glasses" - Checked: true
property-setup-modal.js:833 Amenity 29: "Baking sheet" - Checked: true
property-setup-modal.js:833 Amenity 30: "Barbecue utensils" - Checked: true
property-setup-modal.js:833 Amenity 31: "Dining table" - Checked: true
property-setup-modal.js:833 Amenity 32: "Laundromat nearby" - Checked: true
property-setup-modal.js:833 Amenity 33: "Outdoor furniture" - Checked: true
property-setup-modal.js:833 Amenity 34: "Outdoor dining area" - Checked: true
property-setup-modal.js:833 Amenity 35: "BBQ grill" - Checked: true
property-setup-modal.js:833 Amenity 36: "Free street parking" - Checked: true
property-setup-modal.js:833 Amenity 37: "Private living room" - Checked: true
property-setup-modal.js:833 Amenity 38: "Self check-in" - Checked: true
property-setup-modal.js:833 Amenity 39: "Keypad" - Checked: true
property-setup-modal.js:845 Appliances container: <div class=​"space-y-2" id=​"appliances-list">​…​</div>​
property-setup-modal.js:849 Found 9 appliance elements
property-setup-modal.js:853 Appliance 0: Found 4 input fields
property-setup-modal.js:861 Appliance 0 data: Name="TV", Location="Living Room", Brand="", Model=""
property-setup-modal.js:853 Appliance 1: Found 4 input fields
property-setup-modal.js:861 Appliance 1 data: Name="Refrigerator", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 2: Found 4 input fields
property-setup-modal.js:861 Appliance 2 data: Name="Microwave", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 3: Found 4 input fields
property-setup-modal.js:861 Appliance 3 data: Name="Freezer", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 4: Found 4 input fields
property-setup-modal.js:861 Appliance 4 data: Name="Stove", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 5: Found 4 input fields
property-setup-modal.js:861 Appliance 5 data: Name="Oven", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 6: Found 4 input fields
property-setup-modal.js:861 Appliance 6 data: Name="Coffee maker", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 7: Found 4 input fields
property-setup-modal.js:861 Appliance 7 data: Name="Toaster", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 8: Found 4 input fields
property-setup-modal.js:861 Appliance 8 data: Name="Blender", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:877 Collected current amenities: {basic: Array(40), appliances: Array(9)}
property-setup-modal.js:513 Saving basic information with amenities: {name: 'Warm, private room near Harvard and Boston', address: 'Watertown, Massachusetts, United States', description: 'HomesHomesNEWNEWExperiencesExperiences, newNEWNEWS…oved homes on Airbnb, according to guestsRated 4.', icalUrl: 'https://www.airbnb.com/calendar/ical/1376252243023110567.ics?s=afd260cb47e4a62730f56c51a3fd1db1', checkInTime: '15:00', …}
property-setup-modal.js:897 Step 1 saved successfully
property-setup-modal.js:926 Updated amenities in local property data: {basic: Array(40), appliances: Array(9)}
property-setup-modal.js:948 Updated local property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:2569 🔧 Loading unified rules section...
property-setup-modal.js:2573 ✅ Using existing saved house rules: 9
property-setup-modal.js:2608 🔧 Loading original unified rules from import data...
property-setup-modal.js:2723 🔗 Found 2 "Before you leave" rules, concatenating...
property-setup-modal.js:2753 ✅ Created concatenated "Before you leave" rule: Before you leave, clean up, lock up, clean up, lock up.
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "5 guests maximum"
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "Quiet hours: 9:00 PM - 7:00 AM"
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "No parties or events"
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "No smoking"
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "No pets"
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "I am vaccinated. Please no late arrivals past 10pm. Quiet hours from 11pm to 8am. Patio is available from May to October. Street parking is available from end of March to end of November. Guests staying during winter months will have to park in the driveway. It is shared with upstairs neighbors during that time. We can easily coordinate cars."
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "2 guests maximum"
property-setup-modal.js:2779 🚫 Filtered duplicate rule: "Check-in: 3:00 PM - 10:00 PM"
property-setup-modal.js:2783 📋 Deduplicated rules: 17 → 9
property-setup-modal.js:2621 🔍 Debugging imported rules:
property-setup-modal.js:2622   - extractedRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]0: {description: '5 guests maximum', type: 'rule'}1: {description: 'Quiet hours: 9:00 PM - 7:00 AM', type: 'rule'}2: {description: 'No parties or events', type: 'rule'}3: {description: 'No smoking', type: 'rule'}4: {description: 'No pets', type: 'rule'}5: {description: 'Before you leave: clean up, lock up', type: 'instruction'}6: {description: 'I am vaccinated. Please no late arrivals past 10pm… during that time. We can easily coordinate cars.', enabled: true, source: 'airbnb_pattern_extraction', title: 'Quiet hours (10pm - 11pm)', type: 'rule'}7: {description: '2 guests maximum', enabled: true, source: 'airbnb_pattern_extraction', title: 'Maximum 2 guests', type: 'rule'}8: {description: 'Check-in: 3:00 PM - 10:00 PM', enabled: true, source: 'airbnb_time_pattern_extraction', title: 'Check-in', type: 'rule'}length: 9[[Prototype]]: Array(0)
property-setup-modal.js:2623   - deepExtractedRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]0: {description: '5 guests maximum', type: 'rule'}1: {description: 'Quiet hours: 9:00 PM - 7:00 AM', type: 'rule'}2: {description: 'No parties or events', type: 'rule'}3: {description: 'No smoking', type: 'rule'}4: {description: 'No pets', type: 'rule'}5: {description: 'Before you leave: clean up, lock up', type: 'instruction'}6: {description: 'I am vaccinated. Please no late arrivals past 10pm… during that time. We can easily coordinate cars.', enabled: true, source: 'airbnb_pattern_extraction', title: 'Quiet hours (10pm - 11pm)', type: 'rule'}7: {description: '2 guests maximum', enabled: true, source: 'airbnb_pattern_extraction', title: 'Maximum 2 guests', type: 'rule'}8: {description: 'Check-in: 3:00 PM - 10:00 PM', enabled: true, source: 'airbnb_time_pattern_extraction', title: 'Check-in', type: 'rule'}length: 9[[Prototype]]: Array(0)
property-setup-modal.js:2624   - allImportedRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]0: {description: '5 guests maximum', type: 'rule'}1: {description: 'Quiet hours: 9:00 PM - 7:00 AM', type: 'rule'}2: {description: 'No parties or events', type: 'rule'}3: {description: 'No smoking', type: 'rule'}4: {description: 'No pets', type: 'rule'}5: {description: 'I am vaccinated. Please no late arrivals past 10pm… during that time. We can easily coordinate cars.', enabled: true, source: 'airbnb_pattern_extraction', title: 'Quiet hours (10pm - 11pm)', type: 'rule'}6: {description: '2 guests maximum', enabled: true, source: 'airbnb_pattern_extraction', title: 'Maximum 2 guests', type: 'rule'}7: {description: 'Check-in: 3:00 PM - 10:00 PM', enabled: true, source: 'airbnb_time_pattern_extraction', title: 'Check-in', type: 'rule'}8: {id: 'before_you_leave_combined', title: 'Before you leave', description: 'Before you leave, clean up, lock up, clean up, lock up.', content: 'Before you leave, clean up, lock up, clean up, lock up.', type: 'imported'}length: 9[[Prototype]]: Array(0)
property-setup-modal.js:2625   - propertyData.importData: {extractedAt: '2025-07-21T19:42:21.696442', rawData: {…}, source: 'airbnb'}
property-setup-modal.js:2626   - propertyData.importData?.rawData: {extracted: {…}, house_rules: Array(9), listing: {…}}
property-setup-modal.js:2669 Filtered to 9 valid imported rules
property-setup-modal.js:2872 🚫 Conflict detected: Default rule "Smoking" conflicts with imported rule "No smoking"
property-setup-modal.js:2872 🚫 Conflict detected: Default rule "Parties and Events" conflicts with imported rule "No parties or events"
property-setup-modal.js:2872 🚫 Conflict detected: Default rule "Quiet Hours" conflicts with imported rule "Quiet hours: 9:00 PM - 7:00 AM"
property-setup-modal.js:2872 🚫 Conflict detected: Default rule "Pets" conflicts with imported rule "No pets"
property-setup-modal.js:2872 🚫 Conflict detected: Default rule "Property Capacity" conflicts with imported rule "5 guests maximum"
property-setup-modal.js:2675 Using 2 default rules (5 filtered out due to conflicts)
property-setup-modal.js:2683 Total unified rules: 11
property-setup-modal.js:2980 ✅ Rendered 11 unified rules (9 imported, 2 default)
property-setup-modal.js:1024 Step indicator element: <div id=​"step-indicator" class=​"text-sm text-gray-600">​Step 2 of 5: House Rules​</div>​
property-setup-modal.js:1025 Current step: 2 Step name: House Rules
property-setup-modal.js:1028 Updating step indicator to: Step 2 of 5: House Rules
property-setup-modal.js:2980 ✅ Rendered 11 unified rules (9 imported, 2 default)
property-setup-modal.js:3199 Uncaught TypeError: Cannot read properties of null (reading 'checked')
    at PropertySetupModal.toggleRule (property-setup-modal.js:3199:36)
    at HTMLInputElement.onchange (dashboard:1:20)
toggleRule @ property-setup-modal.js:3199
onchange @ dashboard:1Understand this error
property-setup-modal.js:2991 House rule checkbox 10 changed to: true
property-setup-modal.js:3010 Updated house rule 10 enabled status: true
property-setup-modal.js:3018 Auto-saving house rules...
property-setup-modal.js:3051 Updated current house rules from form: (11) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]0: {description: '5 guests maximum', type: 'rule', enabled: true, source: 'imported', title: undefined, …}1: {description: '5 guests maximum', type: 'rule', enabled: true, source: 'imported', title: undefined, …}2: {description: '5 guests maximum', type: 'rule', enabled: true, source: 'imported', title: undefined, …}3: {description: '5 guests maximum', type: 'rule', enabled: true, source: 'imported', title: undefined, …}4: {description: '5 guests maximum', type: 'rule', enabled: true, source: 'imported', title: undefined, …}5: {description: 'I am vaccinated. Please no late arrivals past 10pm… during that time. We can easily coordinate cars.', enabled: true, source: 'imported', title: 'Quiet hours (10pm - 11pm)', type: 'rule', …}6: {description: '2 guests maximum', enabled: true, source: 'imported', title: 'Maximum 2 guests', type: 'rule', …}7: {description: 'Check-in: 3:00 PM - 10:00 PM', enabled: true, source: 'imported', title: 'Check-in', type: 'rule', …}8: {id: 'before_you_leave_combined', title: 'Before you leave', description: 'Before you leave, clean up, lock up, clean up, lock up.', content: 'Before you leave, clean up, lock up, clean up, lock up.', type: 'imported', …}9: {id: 'commercial_photography', title: 'Commercial Photography', content: 'No commercial photography or filming without prior approval', enabled: false, type: 'default', …}10: {id: 'shoes', title: 'Shoes in Property', content: 'Remove shoes when entering the property', enabled: true, type: 'default', …}length: 11[[Prototype]]: Array(0)
property-setup-modal.js:897 Step 2 saved successfully
property-setup-modal.js:932 Updated house rules in local property data: (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]0: {description: '5 guests maximum', type: 'rule', enabled: true, source: 'imported', title: undefined, …}1: {description: '5 guests maximum', type: 'rule', enabled: true, source: 'imported', title: undefined, …}2: {description: '5 guests maximum', type: 'rule', enabled: true, source: 'imported', title: undefined, …}3: {description: '5 guests maximum', type: 'rule', enabled: true, source: 'imported', title: undefined, …}4: {description: '5 guests maximum', type: 'rule', enabled: true, source: 'imported', title: undefined, …}5: {description: 'I am vaccinated. Please no late arrivals past 10pm… during that time. We can easily coordinate cars.', enabled: true, source: 'imported', title: 'Quiet hours (10pm - 11pm)', type: 'rule', …}6: {description: '2 guests maximum', enabled: true, source: 'imported', title: 'Maximum 2 guests', type: 'rule', …}7: {description: 'Check-in: 3:00 PM - 10:00 PM', enabled: true, source: 'imported', title: 'Check-in', type: 'rule', …}8: {id: 'before_you_leave_combined', title: 'Before you leave', description: 'Before you leave, clean up, lock up, clean up, lock up.', content: 'Before you leave, clean up, lock up, clean up, lock up.', type: 'imported', …}9: {id: 'shoes', title: 'Shoes in Property', content: 'Remove shoes when entering the property', enabled: true, type: 'default', …}length: 10[[Prototype]]: Array(0)
property-setup-modal.js:948 Updated local property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}address: "Watertown, Massachusetts, United States"airbnbListingUrl: "https://www.airbnb.com/rooms/18839204"amenities: {basic: Array(40), appliances: Array(9)}checkInTime: "15:00"checkOutTime: "11:00"createdAt: "Tue, 22 Jul 2025 00:42:21 GMT"description: "HomesHomesNEWNEWExperiencesExperiences, newNEWNEWServicesServices, newStart your searchLocationAnywhereCheck in / Check outAny weekGuestsAdd guestsBecome a hostWarm, private room near Harvard and BostonShareSaveShow all photosPhotosAmenitiesReviewsLocationAdd dates for pricesAdd dates for prices4. 85 ·246 reviewsRated 4. 85 out of 5 from 246 reviews. Check availabilityRoom in Watertown, Massachusetts1 queen bed··Shared bathroomGuest favoriteOne of the most loved homes on Airbnb, according to guestsRated 4."hostId: "xmM4At4jH3eeNfq69WpBqPyMg952"houseRules: (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]icalUrl: "https://www.airbnb.com/calendar/ical/1376252243023110567.ics?s=afd260cb47e4a62730f56c51a3fd1db1"id: "223f3bd6-a26d-445f-85d2-f7472a6a9e58"importData: {extractedAt: '2025-07-21T19:42:21.696442', rawData: {…}, source: 'airbnb'}name: "Warm, private room near Harvard and Boston"new: truesetupProgress: {step1_basic: true, step2_rules: true, step3_emergency: false, step4_facts: false, step5_review: false}status: "inactive"updatedAt: "Tue, 22 Jul 2025 00:52:13 GMT"wifiDetails: networkName: "welcome123"password: "123welcome#"[[Prototype]]: Object[[Prototype]]: Objectconstructor: ƒ Object()hasOwnProperty: ƒ hasOwnProperty()isPrototypeOf: ƒ isPrototypeOf()propertyIsEnumerable: ƒ propertyIsEnumerable()toLocaleString: ƒ toLocaleString()toString: ƒ toString()valueOf: ƒ valueOf()__defineGetter__: ƒ __defineGetter__()__defineSetter__: ƒ __defineSetter__()__lookupGetter__: ƒ __lookupGetter__()__lookupSetter__: ƒ __lookupSetter__()__proto__: (...)get __proto__: ƒ __proto__()set __proto__: ƒ __proto__()