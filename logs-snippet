Host Dashboard Template Data:
dashboard:820 - Name: Arik
dashboard:821 - Email: Not provided
dashboard:822 - Phone Number: +15551234555
dashboard:823 - User ID: xmM4At4jH3eeNfq69WpBqPyMg952
dashboard:824 - User Role: host
auth.js:290 Checking authentication state...
auth.js:10 Initializing Firebase securely for auth...
dashboard:854 Loading Firebase configuration from secure endpoint...
host_dashboard.js:15 Host Dashboard loaded
host_dashboard.js:91 Loading properties for host from session user
host_dashboard.js:91 Loading properties for host from session user
dashboard:874 Firebase configuration loaded securely
dashboard:879 Firebase initialized securely
auth.js:13 Firebase auth initialized securely
host_dashboard.js:122 API Response: {properties: Array(2), success: true}
host_dashboard.js:124 Properties from API: (2) [{…}, {…}]
host_dashboard.js:170 Creating property card for: d0192bf5-169c-4277-ad4c-2c3e7490eda9
host_dashboard.js:171 Property data: {address: 'Watertown, Massachusetts, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Mon, 21 Jul 2025 21:21:36 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
host_dashboard.js:170 Creating property card for: f369ea47-421a-45fa-8528-9a4815dfad49
host_dashboard.js:171 Property data: {address: 'Watertown, Massachusetts, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Mon, 21 Jul 2025 21:22:00 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
host_dashboard.js:122 API Response: {properties: Array(2), success: true}
host_dashboard.js:124 Properties from API: (2) [{…}, {…}]
host_dashboard.js:170 Creating property card for: d0192bf5-169c-4277-ad4c-2c3e7490eda9
host_dashboard.js:171 Property data: {address: 'Watertown, Massachusetts, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Mon, 21 Jul 2025 21:21:36 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
host_dashboard.js:170 Creating property card for: f369ea47-421a-45fa-8528-9a4815dfad49
host_dashboard.js:171 Property data: {address: 'Watertown, Massachusetts, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Mon, 21 Jul 2025 21:22:00 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
auth.js:309 Auth state changed, user: signed in (xmM4At4jH3eeNfq69WpBqPyMg952)
auth.js:324 User is signed in with Firebase. Updating UI.
host_dashboard.js:1116 Toggle property status: d0192bf5-169c-4277-ad4c-2c3e7490eda9 true
host_dashboard.js:1169 Starting property setup for: d0192bf5-169c-4277-ad4c-2c3e7490eda9
host_dashboard.js:1182 Fetched latest property data for setup: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:40 Opening Property Setup Modal
property-setup-modal.js:41 Property ID: d0192bf5-169c-4277-ad4c-2c3e7490eda9
property-setup-modal.js:42 Property Data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:43 Property amenities: {appliances: Array(9), basic: Array(41)}
property-setup-modal.js:158 Loaded setup progress from property data: {houseRules: 9, emergencyInfo: 0, propertyFacts: 0}
property-setup-modal.js:785 Normalized "Refrigerator" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Microwave" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Freezer" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Stove" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Oven" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Coffee maker" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Toaster" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Blender" location to Kitchen (was: "kitchen")
property-setup-modal.js:803 Enhanced categorization complete: {basicCount: 41, appliancesCount: 9, movedItems: 0}
property-setup-modal.js:343 Loading amenities section
property-setup-modal.js:344 Property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:345 Enhanced amenities data: {basic: Array(41), appliances: Array(9)}
property-setup-modal.js:346 Basic amenities count: 41
property-setup-modal.js:347 Appliances count: 9
property-setup-modal.js:1024 Step indicator element: <div id=​"step-indicator" class=​"text-sm text-gray-600">​Step 1 of 5: Basic Information​</div>​
property-setup-modal.js:1025 Current step: 1 Step name: Basic Information
property-setup-modal.js:1028 Updating step indicator to: Step 1 of 5: Basic Information
property-setup-modal.js:488 Validation errors found: ['iCal URL is required for reservation management']
property-setup-modal.js:609 Scrolled setup-step-content to top
property-setup-modal.js:957 Failed to save current step data
nextStep @ property-setup-modal.js:957
await in nextStep
onclick @ dashboard:1Understand this error
property-setup-modal.js:609 Scrolled setup-step-content to top
property-setup-modal.js:819 🔍 Collecting current amenities...
property-setup-modal.js:823 Basic amenities container: <div class=​"grid grid-cols-1 md:​grid-cols-2 gap-2" id=​"basic-amenities">​…​</div>​grid
property-setup-modal.js:827 Found 41 basic amenity elements
property-setup-modal.js:833 Amenity 0: "Lock on bedroom door" - Checked: true
property-setup-modal.js:833 Amenity 1: "Kitchen" - Checked: true
property-setup-modal.js:833 Amenity 2: "Wifi" - Checked: true
property-setup-modal.js:833 Amenity 3: "Free parking on premises" - Checked: true
property-setup-modal.js:833 Amenity 4: "Air conditioning" - Checked: true
property-setup-modal.js:833 Amenity 5: "Bathtub" - Checked: true
property-setup-modal.js:833 Amenity 6: "Shared patio or balcony" - Checked: true
property-setup-modal.js:833 Amenity 7: "Shared backyard – Fully fenced" - Checked: true
property-setup-modal.js:833 Amenity 8: "Cleaning products" - Checked: true
property-setup-modal.js:833 Amenity 9: "Jason body soap" - Checked: true
property-setup-modal.js:833 Amenity 10: "Hot water" - Checked: true
property-setup-modal.js:833 Amenity 11: "Essentials" - Checked: true
property-setup-modal.js:833 Amenity 12: "Hangers" - Checked: true
property-setup-modal.js:833 Amenity 13: "Bed linens" - Checked: true
property-setup-modal.js:833 Amenity 14: "Extra pillows and blankets" - Checked: true
property-setup-modal.js:833 Amenity 15: "Room-darkening shades" - Checked: true
property-setup-modal.js:833 Amenity 16: "Iron" - Checked: true
property-setup-modal.js:833 Amenity 17: "Safe" - Checked: true
property-setup-modal.js:833 Amenity 18: "Clothing storage: closet and dresser" - Checked: true
property-setup-modal.js:833 Amenity 19: "Game console: PS4" - Checked: true
property-setup-modal.js:833 Amenity 20: "Books and reading material" - Checked: true
property-setup-modal.js:833 Amenity 21: "Portable fans" - Checked: true
property-setup-modal.js:833 Amenity 22: "Heating" - Checked: true
property-setup-modal.js:833 Amenity 23: "Smoke alarm" - Checked: true
property-setup-modal.js:833 Amenity 24: "Carbon monoxide alarm" - Checked: true
property-setup-modal.js:833 Amenity 25: "Fire extinguisher" - Checked: true
property-setup-modal.js:833 Amenity 26: "Cooking basics" - Checked: true
property-setup-modal.js:833 Amenity 27: "Dishes and silverware" - Checked: true
property-setup-modal.js:833 Amenity 28: "Wine glasses" - Checked: true
property-setup-modal.js:833 Amenity 29: "Baking sheet" - Checked: true
property-setup-modal.js:833 Amenity 30: "Barbecue utensils" - Checked: true
property-setup-modal.js:833 Amenity 31: "Dining table" - Checked: true
property-setup-modal.js:833 Amenity 32: "Laundromat nearby" - Checked: true
property-setup-modal.js:833 Amenity 33: "Outdoor furniture" - Checked: true
property-setup-modal.js:833 Amenity 34: "Outdoor dining area" - Checked: true
property-setup-modal.js:833 Amenity 35: "BBQ grill" - Checked: true
property-setup-modal.js:833 Amenity 36: "Free street parking" - Checked: true
property-setup-modal.js:833 Amenity 37: "Private living room" - Checked: true
property-setup-modal.js:833 Amenity 38: "Self check-in" - Checked: true
property-setup-modal.js:833 Amenity 39: "Computer" - Checked: true
property-setup-modal.js:833 Amenity 40: "Locker" - Checked: true
property-setup-modal.js:845 Appliances container: <div class=​"space-y-2" id=​"appliances-list">​…​</div>​
property-setup-modal.js:849 Found 9 appliance elements
property-setup-modal.js:853 Appliance 0: Found 4 input fields
property-setup-modal.js:861 Appliance 0 data: Name="TV", Location="Living Room", Brand="", Model=""
property-setup-modal.js:853 Appliance 1: Found 4 input fields
property-setup-modal.js:861 Appliance 1 data: Name="Refrigerator", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 2: Found 4 input fields
property-setup-modal.js:861 Appliance 2 data: Name="Microwave", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 3: Found 4 input fields
property-setup-modal.js:861 Appliance 3 data: Name="Freezer", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 4: Found 4 input fields
property-setup-modal.js:861 Appliance 4 data: Name="Stove", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 5: Found 4 input fields
property-setup-modal.js:861 Appliance 5 data: Name="Oven", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 6: Found 4 input fields
property-setup-modal.js:861 Appliance 6 data: Name="Coffee maker", Location="Kitchen", Brand="Keurig", Model="TU-78"
property-setup-modal.js:853 Appliance 7: Found 4 input fields
property-setup-modal.js:861 Appliance 7 data: Name="Toaster", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 8: Found 4 input fields
property-setup-modal.js:861 Appliance 8 data: Name="Blender", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:877 Collected current amenities: {basic: Array(41), appliances: Array(9)}
property-setup-modal.js:513 Saving basic information with amenities: {name: 'Private Room near Harvard & Boston', address: 'Watertown, Massachusetts, United States', description: 'This private room in a Watertown home offers a gli…acy and connection while experiencing local life.', icalUrl: 'https://www.airbnb.com/calendar/ical/1376252243023110567.ics?s=afd260cb47e4a62730f56c51a3fd1db1', checkInTime: '15:00', …}
property-setup-modal.js:819 🔍 Collecting current amenities...
property-setup-modal.js:823 Basic amenities container: <div class=​"grid grid-cols-1 md:​grid-cols-2 gap-2" id=​"basic-amenities">​…​</div>​grid
property-setup-modal.js:827 Found 41 basic amenity elements
property-setup-modal.js:833 Amenity 0: "Lock on bedroom door" - Checked: true
property-setup-modal.js:833 Amenity 1: "Kitchen" - Checked: true
property-setup-modal.js:833 Amenity 2: "Wifi" - Checked: true
property-setup-modal.js:833 Amenity 3: "Free parking on premises" - Checked: true
property-setup-modal.js:833 Amenity 4: "Air conditioning" - Checked: true
property-setup-modal.js:833 Amenity 5: "Bathtub" - Checked: true
property-setup-modal.js:833 Amenity 6: "Shared patio or balcony" - Checked: true
property-setup-modal.js:833 Amenity 7: "Shared backyard – Fully fenced" - Checked: true
property-setup-modal.js:833 Amenity 8: "Cleaning products" - Checked: true
property-setup-modal.js:833 Amenity 9: "Jason body soap" - Checked: true
property-setup-modal.js:833 Amenity 10: "Hot water" - Checked: true
property-setup-modal.js:833 Amenity 11: "Essentials" - Checked: true
property-setup-modal.js:833 Amenity 12: "Hangers" - Checked: true
property-setup-modal.js:833 Amenity 13: "Bed linens" - Checked: true
property-setup-modal.js:833 Amenity 14: "Extra pillows and blankets" - Checked: true
property-setup-modal.js:833 Amenity 15: "Room-darkening shades" - Checked: true
property-setup-modal.js:833 Amenity 16: "Iron" - Checked: true
property-setup-modal.js:833 Amenity 17: "Safe" - Checked: true
property-setup-modal.js:833 Amenity 18: "Clothing storage: closet and dresser" - Checked: true
property-setup-modal.js:833 Amenity 19: "Game console: PS4" - Checked: true
property-setup-modal.js:833 Amenity 20: "Books and reading material" - Checked: true
property-setup-modal.js:833 Amenity 21: "Portable fans" - Checked: true
property-setup-modal.js:833 Amenity 22: "Heating" - Checked: true
property-setup-modal.js:833 Amenity 23: "Smoke alarm" - Checked: true
property-setup-modal.js:833 Amenity 24: "Carbon monoxide alarm" - Checked: true
property-setup-modal.js:833 Amenity 25: "Fire extinguisher" - Checked: true
property-setup-modal.js:833 Amenity 26: "Cooking basics" - Checked: true
property-setup-modal.js:833 Amenity 27: "Dishes and silverware" - Checked: true
property-setup-modal.js:833 Amenity 28: "Wine glasses" - Checked: true
property-setup-modal.js:833 Amenity 29: "Baking sheet" - Checked: true
property-setup-modal.js:833 Amenity 30: "Barbecue utensils" - Checked: true
property-setup-modal.js:833 Amenity 31: "Dining table" - Checked: true
property-setup-modal.js:833 Amenity 32: "Laundromat nearby" - Checked: true
property-setup-modal.js:833 Amenity 33: "Outdoor furniture" - Checked: true
property-setup-modal.js:833 Amenity 34: "Outdoor dining area" - Checked: true
property-setup-modal.js:833 Amenity 35: "BBQ grill" - Checked: true
property-setup-modal.js:833 Amenity 36: "Free street parking" - Checked: true
property-setup-modal.js:833 Amenity 37: "Private living room" - Checked: true
property-setup-modal.js:833 Amenity 38: "Self check-in" - Checked: true
property-setup-modal.js:833 Amenity 39: "Computer" - Checked: true
property-setup-modal.js:833 Amenity 40: "Locker" - Checked: true
property-setup-modal.js:845 Appliances container: <div class=​"space-y-2" id=​"appliances-list">​…​</div>​
property-setup-modal.js:849 Found 9 appliance elements
property-setup-modal.js:853 Appliance 0: Found 4 input fields
property-setup-modal.js:861 Appliance 0 data: Name="TV", Location="Living Room", Brand="", Model=""
property-setup-modal.js:853 Appliance 1: Found 4 input fields
property-setup-modal.js:861 Appliance 1 data: Name="Refrigerator", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 2: Found 4 input fields
property-setup-modal.js:861 Appliance 2 data: Name="Microwave", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 3: Found 4 input fields
property-setup-modal.js:861 Appliance 3 data: Name="Freezer", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 4: Found 4 input fields
property-setup-modal.js:861 Appliance 4 data: Name="Stove", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 5: Found 4 input fields
property-setup-modal.js:861 Appliance 5 data: Name="Oven", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 6: Found 4 input fields
property-setup-modal.js:861 Appliance 6 data: Name="Coffee maker", Location="Kitchen", Brand="Keurig", Model="TU-78"
property-setup-modal.js:853 Appliance 7: Found 4 input fields
property-setup-modal.js:861 Appliance 7 data: Name="Toaster", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 8: Found 4 input fields
property-setup-modal.js:861 Appliance 8 data: Name="Blender", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:877 Collected current amenities: {basic: Array(41), appliances: Array(9)}
property-setup-modal.js:513 Saving basic information with amenities: {name: 'Private Room near Harvard & Boston', address: 'Watertown, Massachusetts, United States', description: 'This private room in a Watertown home offers a gli…acy and connection while experiencing local life.', icalUrl: 'https://www.airbnb.com/calendar/ical/1376252243023110567.ics?s=afd260cb47e4a62730f56c51a3fd1db1', checkInTime: '15:00', …}
property-setup-modal.js:897 Step 1 saved successfully
property-setup-modal.js:926 Updated amenities in local property data: {basic: Array(41), appliances: Array(9)}
property-setup-modal.js:948 Updated local property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:897 Step 1 saved successfully
property-setup-modal.js:926 Updated amenities in local property data: {basic: Array(41), appliances: Array(9)}
property-setup-modal.js:948 Updated local property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:2654 🔧 Loading unified rules section...
property-setup-modal.js:2658 ✅ Using existing saved house rules: 9
property-setup-modal.js:2693 🔧 Loading original unified rules from import data...
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "5 guests maximum"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "Quiet hours: 9:00 PM - 7:00 AM"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "No parties or events"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "No smoking"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "Before you leave: clean up, lock up"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "Warm, private room near Harvard and Boston"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "I am vaccinated. Please no late arrivals past 10pm. Quiet hours from 11pm to 8am. Patio is available from May to October. Street parking is available from end of March to end of November. Guests staying during winter months will have to park in the driveway. It is shared with upstairs neighbors during that time. We can easily coordinate cars."
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "2 guests maximum"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "Check-in: 3:00 PM - 10:00 PM"
property-setup-modal.js:2796 📋 Deduplicated rules: 18 → 9
property-setup-modal.js:2703 🔍 Debugging imported rules:
property-setup-modal.js:2704   - extractedRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:2705   - deepExtractedRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:2706   - allImportedRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:2707   - propertyData.importData: {extractedAt: '2025-07-21T16:21:36.878735', rawData: {…}, source: 'airbnb'}
property-setup-modal.js:2708   - propertyData.importData?.rawData: {extracted: {…}, house_rules: Array(9), listing: {…}}
property-setup-modal.js:2751 Filtered to 9 valid imported rules
property-setup-modal.js:2865 🚫 Conflict detected: Default rule "No smoking" conflicts with imported rule "No smoking"
property-setup-modal.js:2865 🚫 Conflict detected: Default rule "No parties or events" conflicts with imported rule "No parties or events"
property-setup-modal.js:2865 🚫 Conflict detected: Default rule "Quiet hours" conflicts with imported rule "Quiet hours: 9:00 PM - 7:00 AM"
property-setup-modal.js:2865 🚫 Conflict detected: Default rule "Check-in time" conflicts with imported rule "Check-in"
property-setup-modal.js:2865 🚫 Conflict detected: Default rule "Maximum occupancy" conflicts with imported rule "5 guests maximum"
property-setup-modal.js:2757 Using 5 default rules (5 filtered out due to conflicts)
property-setup-modal.js:2765 Total unified rules: 14
property-setup-modal.js:2974 ✅ Rendered 14 unified rules (9 imported, 5 default)
property-setup-modal.js:1024 Step indicator element: <div id=​"step-indicator" class=​"text-sm text-gray-600">​Step 2 of 5: House Rules​</div>​
property-setup-modal.js:1025 Current step: 2 Step name: House Rules
property-setup-modal.js:1028 Updating step indicator to: Step 2 of 5: House Rules
property-setup-modal.js:2974 ✅ Rendered 14 unified rules (9 imported, 5 default)
host_dashboard.js:1169 Starting property setup for: d0192bf5-169c-4277-ad4c-2c3e7490eda9
host_dashboard.js:1182 Fetched latest property data for setup: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:40 Opening Property Setup Modal
property-setup-modal.js:41 Property ID: d0192bf5-169c-4277-ad4c-2c3e7490eda9
property-setup-modal.js:42 Property Data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:43 Property amenities: {appliances: Array(9), basic: Array(41)}
property-setup-modal.js:158 Loaded setup progress from property data: {houseRules: 9, emergencyInfo: 0, propertyFacts: 0}
property-setup-modal.js:785 Normalized "Refrigerator" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Microwave" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Freezer" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Stove" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Oven" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Coffee maker" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Toaster" location to Kitchen (was: "kitchen")
property-setup-modal.js:785 Normalized "Blender" location to Kitchen (was: "kitchen")
property-setup-modal.js:803 Enhanced categorization complete: {basicCount: 41, appliancesCount: 9, movedItems: 0}
property-setup-modal.js:343 Loading amenities section
property-setup-modal.js:344 Property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:345 Enhanced amenities data: {basic: Array(41), appliances: Array(9)}
property-setup-modal.js:346 Basic amenities count: 41
property-setup-modal.js:347 Appliances count: 9
property-setup-modal.js:1024 Step indicator element: <div id=​"step-indicator" class=​"text-sm text-gray-600">​Step 1 of 5: Basic Information​</div>​
property-setup-modal.js:1025 Current step: 1 Step name: Basic Information
property-setup-modal.js:1028 Updating step indicator to: Step 1 of 5: Basic Information
property-setup-modal.js:819 🔍 Collecting current amenities...
property-setup-modal.js:823 Basic amenities container: <div class=​"grid grid-cols-1 md:​grid-cols-2 gap-2" id=​"basic-amenities">​…​</div>​grid
property-setup-modal.js:827 Found 41 basic amenity elements
property-setup-modal.js:833 Amenity 0: "Lock on bedroom door" - Checked: true
property-setup-modal.js:833 Amenity 1: "Kitchen" - Checked: true
property-setup-modal.js:833 Amenity 2: "Wifi" - Checked: true
property-setup-modal.js:833 Amenity 3: "Free parking on premises" - Checked: true
property-setup-modal.js:833 Amenity 4: "Air conditioning" - Checked: true
property-setup-modal.js:833 Amenity 5: "Bathtub" - Checked: true
property-setup-modal.js:833 Amenity 6: "Shared patio or balcony" - Checked: true
property-setup-modal.js:833 Amenity 7: "Shared backyard – Fully fenced" - Checked: true
property-setup-modal.js:833 Amenity 8: "Cleaning products" - Checked: true
property-setup-modal.js:833 Amenity 9: "Jason body soap" - Checked: true
property-setup-modal.js:833 Amenity 10: "Hot water" - Checked: true
property-setup-modal.js:833 Amenity 11: "Essentials" - Checked: true
property-setup-modal.js:833 Amenity 12: "Hangers" - Checked: true
property-setup-modal.js:833 Amenity 13: "Bed linens" - Checked: true
property-setup-modal.js:833 Amenity 14: "Extra pillows and blankets" - Checked: true
property-setup-modal.js:833 Amenity 15: "Room-darkening shades" - Checked: true
property-setup-modal.js:833 Amenity 16: "Iron" - Checked: true
property-setup-modal.js:833 Amenity 17: "Safe" - Checked: true
property-setup-modal.js:833 Amenity 18: "Clothing storage: closet and dresser" - Checked: true
property-setup-modal.js:833 Amenity 19: "Game console: PS4" - Checked: true
property-setup-modal.js:833 Amenity 20: "Books and reading material" - Checked: true
property-setup-modal.js:833 Amenity 21: "Portable fans" - Checked: true
property-setup-modal.js:833 Amenity 22: "Heating" - Checked: true
property-setup-modal.js:833 Amenity 23: "Smoke alarm" - Checked: true
property-setup-modal.js:833 Amenity 24: "Carbon monoxide alarm" - Checked: true
property-setup-modal.js:833 Amenity 25: "Fire extinguisher" - Checked: true
property-setup-modal.js:833 Amenity 26: "Cooking basics" - Checked: true
property-setup-modal.js:833 Amenity 27: "Dishes and silverware" - Checked: true
property-setup-modal.js:833 Amenity 28: "Wine glasses" - Checked: true
property-setup-modal.js:833 Amenity 29: "Baking sheet" - Checked: true
property-setup-modal.js:833 Amenity 30: "Barbecue utensils" - Checked: true
property-setup-modal.js:833 Amenity 31: "Dining table" - Checked: true
property-setup-modal.js:833 Amenity 32: "Laundromat nearby" - Checked: true
property-setup-modal.js:833 Amenity 33: "Outdoor furniture" - Checked: true
property-setup-modal.js:833 Amenity 34: "Outdoor dining area" - Checked: true
property-setup-modal.js:833 Amenity 35: "BBQ grill" - Checked: true
property-setup-modal.js:833 Amenity 36: "Free street parking" - Checked: true
property-setup-modal.js:833 Amenity 37: "Private living room" - Checked: true
property-setup-modal.js:833 Amenity 38: "Self check-in" - Checked: true
property-setup-modal.js:833 Amenity 39: "Computer" - Checked: true
property-setup-modal.js:833 Amenity 40: "Locker" - Checked: true
property-setup-modal.js:845 Appliances container: <div class=​"space-y-2" id=​"appliances-list">​…​</div>​
property-setup-modal.js:849 Found 9 appliance elements
property-setup-modal.js:853 Appliance 0: Found 4 input fields
property-setup-modal.js:861 Appliance 0 data: Name="TV", Location="Living Room", Brand="", Model=""
property-setup-modal.js:853 Appliance 1: Found 4 input fields
property-setup-modal.js:861 Appliance 1 data: Name="Refrigerator", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 2: Found 4 input fields
property-setup-modal.js:861 Appliance 2 data: Name="Microwave", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 3: Found 4 input fields
property-setup-modal.js:861 Appliance 3 data: Name="Freezer", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 4: Found 4 input fields
property-setup-modal.js:861 Appliance 4 data: Name="Stove", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 5: Found 4 input fields
property-setup-modal.js:861 Appliance 5 data: Name="Oven", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 6: Found 4 input fields
property-setup-modal.js:861 Appliance 6 data: Name="Coffee maker", Location="Kitchen", Brand="Keurig", Model="TU-78"
property-setup-modal.js:853 Appliance 7: Found 4 input fields
property-setup-modal.js:861 Appliance 7 data: Name="Toaster", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:853 Appliance 8: Found 4 input fields
property-setup-modal.js:861 Appliance 8 data: Name="Blender", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:877 Collected current amenities: {basic: Array(41), appliances: Array(9)}
property-setup-modal.js:513 Saving basic information with amenities: {name: 'Private Room near Harvard & Boston', address: 'Watertown, Massachusetts, United States', description: 'This private room in a Watertown home offers a gli…acy and connection while experiencing local life.', icalUrl: 'https://www.airbnb.com/calendar/ical/1376252243023110567.ics?s=afd260cb47e4a62730f56c51a3fd1db1', checkInTime: '15:00', …}
property-setup-modal.js:897 Step 1 saved successfully
property-setup-modal.js:926 Updated amenities in local property data: {basic: Array(41), appliances: Array(9)}
property-setup-modal.js:948 Updated local property data: {address: 'Watertown, Massachusetts, United States', airbnbListingUrl: 'https://www.airbnb.com/rooms/18839204', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', …}
property-setup-modal.js:2654 🔧 Loading unified rules section...
property-setup-modal.js:2658 ✅ Using existing saved house rules: 9
property-setup-modal.js:2693 🔧 Loading original unified rules from import data...
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "5 guests maximum"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "Quiet hours: 9:00 PM - 7:00 AM"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "No parties or events"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "No smoking"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "Before you leave: clean up, lock up"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "Warm, private room near Harvard and Boston"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "I am vaccinated. Please no late arrivals past 10pm. Quiet hours from 11pm to 8am. Patio is available from May to October. Street parking is available from end of March to end of November. Guests staying during winter months will have to park in the driveway. It is shared with upstairs neighbors during that time. We can easily coordinate cars."
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "2 guests maximum"
property-setup-modal.js:2792 🚫 Filtered duplicate rule: "Check-in: 3:00 PM - 10:00 PM"
property-setup-modal.js:2796 📋 Deduplicated rules: 18 → 9
property-setup-modal.js:2703 🔍 Debugging imported rules:
property-setup-modal.js:2704   - extractedRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:2705   - deepExtractedRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:2706   - allImportedRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:2707   - propertyData.importData: {extractedAt: '2025-07-21T16:21:36.878735', rawData: {…}, source: 'airbnb'}
property-setup-modal.js:2708   - propertyData.importData?.rawData: {extracted: {…}, house_rules: Array(9), listing: {…}}
property-setup-modal.js:2751 Filtered to 9 valid imported rules
property-setup-modal.js:2865 🚫 Conflict detected: Default rule "No smoking" conflicts with imported rule "No smoking"
property-setup-modal.js:2865 🚫 Conflict detected: Default rule "No parties or events" conflicts with imported rule "No parties or events"
property-setup-modal.js:2865 🚫 Conflict detected: Default rule "Quiet hours" conflicts with imported rule "Quiet hours: 9:00 PM - 7:00 AM"
property-setup-modal.js:2865 🚫 Conflict detected: Default rule "Check-in time" conflicts with imported rule "Check-in"
property-setup-modal.js:2865 🚫 Conflict detected: Default rule "Maximum occupancy" conflicts with imported rule "5 guests maximum"
property-setup-modal.js:2757 Using 5 default rules (5 filtered out due to conflicts)
property-setup-modal.js:2765 Total unified rules: 14
property-setup-modal.js:2974 ✅ Rendered 14 unified rules (9 imported, 5 default)
property-setup-modal.js:1024 Step indicator element: <div id=​"step-indicator" class=​"text-sm text-gray-600">​Step 2 of 5: House Rules​</div>​
property-setup-modal.js:1025 Current step: 2 Step name: House Rules
property-setup-modal.js:1028 Updating step indicator to: Step 2 of 5: House Rules
property-setup-modal.js:2974 ✅ Rendered 14 unified rules (9 imported, 5 default)