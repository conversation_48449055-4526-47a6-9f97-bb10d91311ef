/**
 * Property Setup Modal JavaScript
 * Handles the 5-step property setup process for new imported properties
 */

class PropertySetupModal {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 5;
        this.propertyId = null;
        this.propertyData = {};
        this.categorizationApplied = false; // Flag to prevent duplicate categorization
        this.propertyFactsAutoSaveTimeout = null; // Timeout for property facts auto-save
        this.emergencyInfoAutoSaveTimeout = null; // Timeout for emergency info auto-save
        this.houseRulesAutoSaveTimeout = null; // Timeout for house rules auto-save
        this.setupData = {
            basicInfo: {},
            houseRules: [],
            emergencyInfo: [],
            propertyFacts: [],
            reviewData: {}
        };

        this.stepNames = [
            'Basic Information',
            'House Rules',
            'Emergency Information',
            'Other Information',
            'Review and Approve'
        ];

        this.stepIcons = [
            'fa-info-circle',
            'fa-gavel',
            'fa-exclamation-triangle',
            'fa-clipboard-list',
            'fa-check-circle'
        ];
    }

    open(propertyId, propertyData) {
        this.propertyId = propertyId;
        this.propertyData = propertyData;
        this.currentStep = 1;
        this.categorizationApplied = false; // Reset categorization flag for new property

        // Debug logging
        console.log('Opening Property Setup Modal');
        console.log('Property ID:', propertyId);
        console.log('Property Data:', propertyData);
        console.log('Property amenities:', propertyData?.amenities);

        this.createModal();

        // Load any existing setup progress
        this.loadSetupProgress().then(() => {
            this.loadStep(1);
        });
    }

    createModal() {
        // Remove existing modal if any
        const existingModal = document.getElementById('property-setup-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-75 z-[90] flex items-center justify-center p-4';
        modal.id = 'property-setup-modal';

        modal.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <!-- Header -->
                <div class="p-6 text-white" style="background: linear-gradient(to right, #2a9d8f, #e9c46a);">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-semibold text-white">
                                <i class="fas fa-cog mr-2"></i>Property Setup
                            </h3>
                            <p class="text-white opacity-90 text-sm mt-1">${this.propertyData.name || 'New Property'}</p>
                        </div>
                        <button onclick="propertySetupModal.close()" class="text-white hover:text-gray-200">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="mt-4">
                        <div class="flex items-center justify-between text-sm mb-2 text-white">
                            <span>Step ${this.currentStep} of ${this.totalSteps}</span>
                            <span>${Math.round((this.currentStep / this.totalSteps) * 100)}% Complete</span>
                        </div>
                        <div class="w-full bg-white/20 rounded-full h-2">
                            <div class="bg-white rounded-full h-2 transition-all duration-300" 
                                 style="width: ${(this.currentStep / this.totalSteps) * 100}%"></div>
                        </div>
                        <div class="flex justify-between text-xs mt-2 text-white">
                            ${this.stepNames.map((name, index) =>
                                `<span class="${index + 1 <= this.currentStep ? 'font-medium opacity-100' : 'opacity-80'}">
                                    <span class="step-progress-text" style="display: inline;">${name}</span>
                                    <i class="fas ${this.stepIcons[index]} step-progress-icon" style="display: none;" title="${name}"></i>
                                </span>`
                            ).join('')}
                        </div>
                        <style>
                            @media (max-width: 500px) {
                                #property-setup-modal .step-progress-text { display: none !important; }
                                #property-setup-modal .step-progress-icon { display: inline-block !important; }
                                #property-setup-modal .btn-text { display: none !important; }
                            }
                        </style>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-6 overflow-y-auto" id="setup-step-content" style="max-height: calc(90vh - 220px);">
                    <!-- Content will be loaded here -->
                </div>

                <!-- Footer -->
                <div class="modal-footer bg-gray-50 px-6 py-4 flex justify-between items-center border-t">
                    <button onclick="propertySetupModal.previousStep()"
                            id="prev-btn"
                            class="px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors ${this.currentStep === 1 ? 'opacity-50 cursor-not-allowed' : ''}"
                            ${this.currentStep === 1 ? 'disabled' : ''}>
                        <i class="fas fa-arrow-left mr-2"></i><span class="btn-text">Previous</span>
                    </button>

                    <div id="step-indicator" class="text-sm text-gray-600">
                        Step ${this.currentStep} of ${this.totalSteps}: ${this.stepNames[this.currentStep - 1]}
                    </div>

                    <button onclick="propertySetupModal.nextStep()"
                            id="next-btn"
                            class="px-3 py-2 bg-persian-green text-white rounded-lg hover:bg-persian-green/90 transition-colors">
                        <span class="btn-text">${this.currentStep === this.totalSteps ? 'Complete Setup' : 'Next'}</span>
                        <i class="fas ${this.currentStep === this.totalSteps ? 'fa-check' : 'fa-arrow-right'} ml-2"></i>
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        document.body.style.overflow = 'hidden';

        // Apply responsive behavior immediately
        this.applyResponsiveStyles();

        // Add resize listener for orientation changes
        this.resizeListener = () => this.applyResponsiveStyles();
        window.addEventListener('resize', this.resizeListener);

        // Close on outside click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.close();
            }
        });
    }

    applyResponsiveStyles() {
        // Force responsive behavior with JavaScript
        const isNarrowScreen = window.innerWidth <= 500;
        console.log('Screen width:', window.innerWidth, 'Is narrow:', isNarrowScreen);

        const stepTexts = document.querySelectorAll('#property-setup-modal .step-progress-text');
        const stepIcons = document.querySelectorAll('#property-setup-modal .step-progress-icon');
        const btnTexts = document.querySelectorAll('#property-setup-modal .btn-text');

        if (isNarrowScreen) {
            // Hide text, show icons
            stepTexts.forEach(el => {
                el.style.display = 'none';
                el.style.visibility = 'hidden';
            });
            stepIcons.forEach(el => {
                el.style.display = 'inline-block';
                el.style.visibility = 'visible';
            });
            btnTexts.forEach(el => {
                el.style.display = 'none';
                el.style.visibility = 'hidden';
            });
        } else {
            // Show text, hide icons
            stepTexts.forEach(el => {
                el.style.display = 'inline';
                el.style.visibility = 'visible';
            });
            stepIcons.forEach(el => {
                el.style.display = 'none';
                el.style.visibility = 'hidden';
            });
            btnTexts.forEach(el => {
                el.style.display = 'inline';
                el.style.visibility = 'visible';
            });
        }
    }

    async loadSetupProgress() {
        try {
            // Load setup progress from the property data
            if (this.propertyData) {
                // Load basic information from property data
                this.setupData.basicInfo = {
                    name: this.propertyData.name || '',
                    address: this.propertyData.address || '',
                    description: this.propertyData.description || '',
                    icalUrl: this.propertyData.icalUrl || '',
                    checkInTime: this.propertyData.checkInTime || '15:00',
                    checkOutTime: this.propertyData.checkOutTime || '11:00',
                    wifiDetails: this.propertyData.wifiDetails || { network: '', password: '' },
                    amenities: this.propertyData.amenities || {}
                };

                // Load house rules from property data
                if (this.propertyData.houseRules) {
                    this.setupData.houseRules = this.propertyData.houseRules;
                }

                // Load emergency info from property data
                if (this.propertyData.emergencyInfo) {
                    this.setupData.emergencyInfo = this.propertyData.emergencyInfo;
                    this.currentEmergencyInfo = this.propertyData.emergencyInfo;
                }

                // Load property facts from property data
                if (this.propertyData.propertyFacts) {
                    this.setupData.propertyFacts = this.propertyData.propertyFacts;
                }

                console.log('Loaded setup progress from property data:', {
                    basicInfo: this.setupData.basicInfo ? 'loaded' : 'empty',
                    wifiNetwork: this.setupData.basicInfo?.wifiDetails?.network || 'none',
                    houseRules: this.setupData.houseRules?.length || 0,
                    emergencyInfo: this.setupData.emergencyInfo?.length || 0,
                    propertyFacts: this.setupData.propertyFacts?.length || 0
                });
            }
        } catch (error) {
            console.error('Error loading setup progress:', error);
        }
    }

    loadStep(stepNumber) {
        this.currentStep = stepNumber;
        const content = document.getElementById('setup-step-content');
        
        switch (stepNumber) {
            case 1:
                this.loadBasicInformationStep(content);
                break;
            case 2:
                this.loadHouseRulesStep(content);
                break;
            case 3:
                this.loadEmergencyInformationStep(content);
                break;
            case 4:
                this.loadPropertyFactsStep(content);
                break;
            case 5:
                this.loadReviewAndApproveStep(content);
                break;
        }
        
        this.updateProgressBar();
        this.updateNavigationButtons();
    }

    loadBasicInformationStep(content) {
        content.innerHTML = `
            <div>
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-dark-purple mb-2">
                        <i class="fas fa-info-circle text-persian-green mr-2"></i>
                        Basic Property Information
                    </h4>
                    <p class="text-gray-600">Review and edit the basic information about your property. While you can skip most of the fields, providing more detailed and accurate information will help AI assistant provide better responses to your guests.</p>
                </div>

                <div class="space-y-6">
                    <!-- Property Name -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Property Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="property-name"
                               value="${this.setupData.basicInfo?.name || this.propertyData.name || ''}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green"
                               placeholder="Enter property name"
                               required>
                    </div>

                    <!-- Property Address -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Address <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="property-address"
                               value="${this.setupData.basicInfo?.address || this.propertyData.address || ''}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green"
                               placeholder="Enter property address"
                               required>
                        <p class="text-sm text-gray-500 mt-1">
                            <i class="fas fa-info-circle text-blue-500 mr-1"></i>
                            Providing an accurate address enables AI answers based on your property's location.
                            Examples: "What restaurants are nearby?" or "How do I get to downtown?"
                        </p>
                    </div>

                    <!-- iCal URL -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            iCal URL (Reservation Sync) <span class="text-red-500">*</span>
                        </label>
                        <input type="url"
                               id="ical-url"
                               value="${this.propertyData.icalUrl || ''}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green"
                               placeholder="https://www.airbnb.com/calendar/ical/..."
                               required>
                        <p class="text-sm text-gray-500 mt-1">
                            <i class="fas fa-info-circle text-blue-500 mr-1"></i>
                            Add your Airbnb calendar export URL to automatically sync reservations.
                            Find this in your Airbnb listing → Availability → Find more availability settings like these in the calendar → Connect to another website → Copy the Airbnb calendar link.
                        </p>
                    </div>

                    <!-- Property Description -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea id="property-description"
                                  rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green"
                                  placeholder="Describe your property">${this.propertyData.description || ''}</textarea>
                    </div>

                    <!-- Check-in/Check-out Times -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Check-in Time</label>
                            <input type="time" 
                                   id="checkin-time"
                                   value="${this.propertyData.checkInTime || '15:00'}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Check-out Time</label>
                            <input type="time" 
                                   id="checkout-time"
                                   value="${this.propertyData.checkOutTime || '11:00'}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green">
                        </div>
                    </div>

                    <!-- WiFi Details -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h5 class="font-medium text-gray-900 mb-3">
                            WiFi Information <span class="text-red-500">*</span>
                        </h5>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Network Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text"
                                       id="wifi-network"
                                       value="${this.setupData.basicInfo?.wifiDetails?.network || this.propertyData.wifiDetails?.network || ''}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green"
                                       placeholder="WiFi network name"
                                       required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Password <span class="text-red-500">*</span>
                                </label>
                                <input type="text"
                                       id="wifi-password"
                                       value="${this.setupData.basicInfo?.wifiDetails?.password || this.propertyData.wifiDetails?.password || ''}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green"
                                       placeholder="WiFi password"
                                       required>
                            </div>
                        </div>
                    </div>

                    <!-- Amenities Section -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h5 class="font-medium text-gray-900 mb-3">Amenities</h5>
                        <div id="amenities-section">
                            <!-- Amenities will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Load amenities
        this.loadAmenitiesSection();
        
        // Add auto-save listeners
        this.addAutoSaveListeners();
    }

    loadAmenitiesSection() {
        const amenitiesSection = document.getElementById('amenities-section');
        let amenities = this.propertyData.amenities || { basic: [], appliances: [] };

        // Enhanced appliance categorization - only run once to avoid duplicates
        if (!this.categorizationApplied) {
            amenities = this.enhanceApplianceCategorization(amenities);
            this.categorizationApplied = true;
        }

        // Debug logging
        console.log('Loading amenities section');
        console.log('Property data:', this.propertyData);
        console.log('Enhanced amenities data:', amenities);
        console.log('Basic amenities count:', amenities.basic?.length || 0);
        console.log('Appliances count:', amenities.appliances?.length || 0);
        
        amenitiesSection.innerHTML = `
            <div class="space-y-4">
                <!-- Basic Amenities -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Basic Amenities</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2" id="basic-amenities">
                        ${amenities.basic.map((amenity, index) => `
                            <div class="flex items-center space-x-2 p-2 bg-white rounded border">
                                <input type="checkbox" checked class="text-persian-green flex-shrink-0">
                                ${amenity.trim() === '' ? `
                                    <input type="text"
                                           value="${amenity}"
                                           placeholder="Enter amenity name"
                                           class="flex-1 px-2 py-1 border rounded text-sm"
                                           onchange="propertySetupModal.updateBasicAmenity(${index}, this.value)"
                                           onblur="propertySetupModal.updateBasicAmenity(${index}, this.value)">
                                ` : `
                                    <span class="text-sm flex-1">${amenity}</span>
                                `}
                                <button onclick="propertySetupModal.removeBasicAmenity(${index})"
                                        class="flex-shrink-0 text-red-500 hover:text-red-700">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </div>
                        `).join('')}
                    </div>
                    <button onclick="propertySetupModal.addBasicAmenity()"
                            class="mt-2 text-sm text-persian-green hover:text-persian-green/80">
                        <i class="fas fa-plus mr-1"></i>Add Amenity
                    </button>
                </div>

                <!-- Appliances -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Appliances</label>
                    <div class="space-y-2" id="appliances-list">
                        ${amenities.appliances.map((appliance, index) => `
                            <div class="p-3 bg-white rounded border">
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-2">
                                    <input type="text" value="${appliance.name}" placeholder="Appliance name"
                                           class="px-2 py-1 border rounded text-sm"
                                           onchange="propertySetupModal.updateAppliance(${index}, 'name', this.value)">
                                    <input type="text" value="${appliance.location}" placeholder="Location"
                                           class="px-2 py-1 border rounded text-sm"
                                           onchange="propertySetupModal.updateAppliance(${index}, 'location', this.value)">
                                    <input type="text" value="${appliance.brand}" placeholder="Brand"
                                           class="px-2 py-1 border rounded text-sm"
                                           onchange="propertySetupModal.updateAppliance(${index}, 'brand', this.value)">
                                    <div class="flex items-center space-x-2">
                                        <input type="text" value="${appliance.model}" placeholder="Model"
                                               class="px-2 py-1 border rounded text-sm flex-1"
                                               onchange="propertySetupModal.updateAppliance(${index}, 'model', this.value)">
                                        <button onclick="propertySetupModal.removeAppliance(${index})"
                                                class="text-red-500 hover:text-red-700">
                                            <i class="fas fa-trash text-xs"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    <button onclick="propertySetupModal.addAppliance()" 
                            class="mt-2 text-sm text-persian-green hover:text-persian-green/80">
                        <i class="fas fa-plus mr-1"></i>Add Appliance
                    </button>
                </div>
            </div>
        `;
    }

    addAutoSaveListeners() {
        // Add event listeners for auto-saving
        const inputs = document.querySelectorAll('#setup-step-content input, #setup-step-content textarea');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.saveCurrentStepData();
            });
        });
    }

    async saveCurrentStepData() {
        // Save current step data
        switch (this.currentStep) {
            case 1:
                return await this.saveBasicInformation();
            case 2:
                return this.saveHouseRules();
            case 3:
                return this.saveEmergencyInformation();
            case 4:
                return this.savePropertyFacts();
            default:
                return true;
        }
    }

    async saveBasicInformation() {
        // Validate required fields
        const propertyName = document.getElementById('property-name')?.value?.trim() || '';
        const propertyAddress = document.getElementById('property-address')?.value?.trim() || '';
        const icalUrl = document.getElementById('ical-url')?.value?.trim() || '';
        const wifiNetwork = document.getElementById('wifi-network')?.value?.trim() || '';
        const wifiPassword = document.getElementById('wifi-password')?.value?.trim() || '';

        // Check required fields
        const validationErrors = [];

        if (!propertyName) {
            validationErrors.push('Property Name is required');
            this.highlightRequiredField('property-name');
        } else {
            this.clearFieldHighlight('property-name');
        }

        if (!propertyAddress) {
            validationErrors.push('Property Address is required');
            this.highlightRequiredField('property-address');
        } else {
            this.clearFieldHighlight('property-address');
        }

        if (!icalUrl) {
            validationErrors.push('iCal URL is required for reservation management');
            this.highlightRequiredField('ical-url');
        } else {
            this.clearFieldHighlight('ical-url');
        }

        if (!wifiNetwork || !wifiPassword) {
            validationErrors.push('WiFi Network Name and Password are required');
            if (!wifiNetwork) this.highlightRequiredField('wifi-network');
            if (!wifiPassword) this.highlightRequiredField('wifi-password');
        } else {
            this.clearFieldHighlight('wifi-network');
            this.clearFieldHighlight('wifi-password');
        }

        // Show validation errors if any
        if (validationErrors.length > 0) {
            console.log('Validation errors found:', validationErrors);
            this.showValidationErrors(validationErrors);
            return false;
        }

        // Clear any previous validation errors
        this.clearValidationErrors();

        // Collect current amenities and appliances from the form
        const currentAmenities = this.collectCurrentAmenities();

        this.setupData.basicInfo = {
            name: propertyName,
            address: propertyAddress,
            description: document.getElementById('property-description')?.value || '',
            icalUrl: document.getElementById('ical-url')?.value || '',
            checkInTime: document.getElementById('checkin-time')?.value || '15:00',
            checkOutTime: document.getElementById('checkout-time')?.value || '11:00',
            wifiDetails: {
                network: wifiNetwork,
                password: wifiPassword
            },
            amenities: currentAmenities  // Include current amenities state
        };

        console.log('Saving basic information with amenities:', this.setupData.basicInfo);

        // Save to server and wait for completion
        const saved = await this.saveStepToServer(1, this.setupData.basicInfo);
        if (!saved) {
            console.error('Failed to save basic information');
        }
        return saved;
    }

    highlightRequiredField(fieldId) {
        const field = document.getElementById(fieldId);
        if (field) {
            field.classList.add('border-red-500', 'border-2');
            field.classList.remove('border-gray-300');
        }
    }

    clearFieldHighlight(fieldId) {
        const field = document.getElementById(fieldId);
        if (field) {
            field.classList.remove('border-red-500', 'border-2');
            field.classList.add('border-gray-300');
        }
    }

    showValidationErrors(errors) {
        // Remove any existing error display
        this.clearValidationErrors();

        // Create error display
        const errorDiv = document.createElement('div');
        errorDiv.id = 'validation-errors';
        errorDiv.className = 'bg-red-50 border border-red-200 rounded-lg p-4 mb-4';
        errorDiv.innerHTML = `
            <div class="flex items-start space-x-3">
                <i class="fas fa-exclamation-triangle text-red-600 mt-1"></i>
                <div>
                    <h4 class="text-sm font-medium text-red-800 mb-1">Please fix the following errors:</h4>
                    <ul class="text-sm text-red-700 list-disc list-inside">
                        ${errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;

        // Insert at the top of the step content
        const stepContent = document.querySelector('.step-content');
        if (stepContent) {
            stepContent.insertBefore(errorDiv, stepContent.firstChild);
        }

        // Scroll to top to ensure the error message is visible
        this.scrollToTop();
    }

    clearValidationErrors() {
        const errorDiv = document.getElementById('validation-errors');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    showStepError(message) {
        // Remove any existing error display
        this.clearValidationErrors();

        // Create error display
        const errorDiv = document.createElement('div');
        errorDiv.id = 'validation-errors';
        errorDiv.className = 'bg-red-50 border border-red-200 rounded-lg p-4 mb-4';
        errorDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                <span class="text-red-700 font-medium">${message}</span>
            </div>
        `;

        // Insert at the top of the step content
        const stepContent = document.getElementById('setup-step-content');
        if (stepContent) {
            stepContent.insertBefore(errorDiv, stepContent.firstChild);
        }

        // Scroll to top to ensure the error message is visible
        this.scrollToTop();
    }

    scrollToTop() {
        // Primary target: the scrollable step content container
        const stepContent = document.getElementById('setup-step-content');
        if (stepContent) {
            stepContent.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            console.log('Scrolled setup-step-content to top');
            return; // Exit early if successful
        }

        // Fallback 1: try the modal's inner content div
        const modalInner = document.querySelector('#property-setup-modal .bg-white.rounded-lg');
        if (modalInner) {
            modalInner.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            console.log('Scrolled modal inner content to top');
            return;
        }

        // Fallback 2: scroll the entire modal
        const modal = document.getElementById('property-setup-modal');
        if (modal) {
            modal.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            console.log('Scrolled entire modal to top');
        } else {
            console.warn('Could not find any scroll target for validation errors');
        }
    }

    enhanceApplianceCategorization(amenities) {
        // Items that should be moved from basic amenities to appliances
        const applianceKeywords = [
            'TV', 'Television', 'Smart TV', 'HDTV', 'Roku TV', 'Apple TV',
            'Toaster', 'Coffee maker', 'Coffee machine', 'Espresso machine',
            'Microwave', 'Dishwasher', 'Washing machine', 'Washer', 'Dryer',
            'Hair dryer', 'Refrigerator', 'Fridge', 'Oven', 'Stove', 'Cooktop',
            'Freezer', 'Blender', 'Food processor', 'Electric kettle',
            'Rice cooker', 'Slow cooker', 'Air fryer', 'Stand mixer',
            'Ice maker', 'Wine fridge', 'Range', 'Stovetop'
        ];

        // Items that should NOT be appliances (common false positives)
        const nonApplianceKeywords = [
            'Parking', 'Pillows', 'Blankets', 'Books', 'Reading material',
            'Security cameras', 'Cameras', 'Dishes', 'Silverware', 'Wine glasses',
            'Clothing storage', 'Exercise equipment', 'Noise monitors',
            'Decibel monitors', 'Beach access', 'WiFi', 'Internet',
            'Air conditioning', 'Patio', 'Balcony', 'Smart lock', 'Lock',
            'Heating', 'Pool', 'Hot tub', 'Jacuzzi', 'Fireplace', 'Deck'
        ];

        // Kitchen appliances that should have location pre-populated (comprehensive list)
        const kitchenAppliances = [
            'Microwave', 'Dishwasher', 'Refrigerator', 'Fridge', 'Oven',
            'Stove', 'Cooktop', 'Toaster', 'Coffee maker', 'Coffee machine',
            'Espresso machine', 'Freezer', 'Blender', 'Food processor',
            'Electric kettle', 'Rice cooker', 'Slow cooker', 'Air fryer',
            'Stand mixer', 'Ice maker', 'Wine fridge', 'Range', 'Stovetop',
            'Garbage disposal', 'Can opener', 'Mixer', 'Juicer'
        ];

        // Appliances that should NOT get kitchen location (even if they're appliances)
        const nonKitchenAppliances = [
            'Washer', 'Washing machine', 'Dryer', 'Hair dryer', 'TV', 'Television',
            'Air conditioning', 'Heating', 'Vacuum', 'Iron', 'Fan'
        ];

        const enhancedAmenities = {
            basic: [...amenities.basic],
            appliances: [...(amenities.appliances || [])]
        };

        // Move appliance-like items from basic to appliances with flexible matching
        const itemsToMove = [];
        enhancedAmenities.basic.forEach((item, index) => {
            const itemLower = item.toLowerCase();
            let isAppliance = false;
            let isNonAppliance = false;

            // Check for non-appliance keywords first
            for (const keyword of nonApplianceKeywords) {
                const keywordLower = keyword.toLowerCase();
                if (itemLower.includes(keywordLower)) {
                    isNonAppliance = true;
                    break;
                }
            }

            // Check for appliance keywords with flexible matching (if not a non-appliance)
            if (!isNonAppliance) {
                for (const keyword of applianceKeywords) {
                    const keywordLower = keyword.toLowerCase();
                    if (itemLower.includes(keywordLower) ||
                        itemLower.split(' ').some(word => keywordLower.includes(word)) ||
                        keywordLower.split(' ').some(word => itemLower.includes(word))) {
                        isAppliance = true;
                        break;
                    }
                }
            }

            if (isAppliance && !isNonAppliance) {
                itemsToMove.push({ item, index });
            }
        });

        // Remove items from basic amenities (in reverse order to maintain indices)
        itemsToMove.reverse().forEach(({ item, index }) => {
            enhancedAmenities.basic.splice(index, 1);

            // Check if this appliance already exists in appliances array
            const existingAppliance = enhancedAmenities.appliances.find(app =>
                app.name && app.name.toLowerCase() === item.toLowerCase()
            );

            if (!existingAppliance) {
                // Determine if it's a kitchen appliance with flexible matching
                const itemLower = item.toLowerCase();
                let isKitchenAppliance = false;

                for (const keyword of kitchenAppliances) {
                    const keywordLower = keyword.toLowerCase();
                    if (itemLower.includes(keywordLower) ||
                        itemLower.split(' ').some(word => keywordLower.includes(word)) ||
                        keywordLower.split(' ').some(word => itemLower.includes(word))) {
                        isKitchenAppliance = true;
                        break;
                    }
                }

                enhancedAmenities.appliances.push({
                    name: item,
                    location: isKitchenAppliance ? 'Kitchen' : '',
                    brand: '',
                    model: ''
                });

                console.log(`Moved "${item}" to appliances with ${isKitchenAppliance ? 'Kitchen' : 'empty'} location`);
            }
        });

        // Post-process existing appliances to normalize kitchen locations
        enhancedAmenities.appliances.forEach(appliance => {
            if (typeof appliance === 'object' && appliance.name) {
                const itemLower = appliance.name.toLowerCase();
                let isKitchenAppliance = false;

                // Check if it's explicitly a non-kitchen appliance first
                let isNonKitchenAppliance = false;
                for (const keyword of nonKitchenAppliances) {
                    const keywordLower = keyword.toLowerCase();
                    if (itemLower.includes(keywordLower)) {
                        isNonKitchenAppliance = true;
                        break;
                    }
                }

                // Check if it's a kitchen appliance (only if not non-kitchen)
                if (!isNonKitchenAppliance) {
                    for (const keyword of kitchenAppliances) {
                        const keywordLower = keyword.toLowerCase();
                        if (itemLower.includes(keywordLower) ||
                            itemLower.split(' ').some(word => keywordLower.includes(word)) ||
                            keywordLower.split(' ').some(word => itemLower.includes(word))) {
                            isKitchenAppliance = true;
                            break;
                        }
                    }
                }

                // Normalize location for kitchen appliances (but not non-kitchen appliances)
                if (isKitchenAppliance && !isNonKitchenAppliance) {
                    const currentLocation = (appliance.location || '').toLowerCase().trim();
                    const kitchenVariants = ['unit', 'in unit', 'kitchen', ''];

                    if (kitchenVariants.includes(currentLocation)) {
                        appliance.location = 'Kitchen';
                        console.log(`Normalized "${appliance.name}" location to Kitchen (was: "${currentLocation}")`);
                    } else {
                        // Debug: log when kitchen appliances don't get normalized
                        console.log(`⚠️ Kitchen appliance "${appliance.name}" has non-standard location: "${currentLocation}"`);
                    }
                } else if (appliance.name.toLowerCase().includes('dishwasher')) {
                    // Special debug for dishwasher
                    console.log(`🔍 Dishwasher debug: "${appliance.name}"`);
                    console.log(`   isKitchenAppliance: ${isKitchenAppliance}`);
                    console.log(`   isNonKitchenAppliance: ${isNonKitchenAppliance}`);
                    console.log(`   current location: "${appliance.location}"`);
                }
            }
        });

        // Update the property data with enhanced categorization
        this.propertyData.amenities = enhancedAmenities;

        console.log('Enhanced categorization complete:', {
            basicCount: enhancedAmenities.basic.length,
            appliancesCount: enhancedAmenities.appliances.length,
            movedItems: itemsToMove.length
        });

        return enhancedAmenities;
    }

    collectCurrentAmenities() {
        // Collect current amenities and appliances from the form
        const amenities = {
            basic: [],
            appliances: []
        };

        console.log('🔍 Collecting current amenities...');

        // Collect basic amenities
        const basicAmenitiesContainer = document.getElementById('basic-amenities');
        console.log('Basic amenities container:', basicAmenitiesContainer);

        if (basicAmenitiesContainer) {
            const amenityElements = basicAmenitiesContainer.querySelectorAll('div.flex.items-center');
            console.log(`Found ${amenityElements.length} basic amenity elements`);

            amenityElements.forEach((element, index) => {
                const checkbox = element.querySelector('input[type="checkbox"]');
                const span = element.querySelector('span');
                if (checkbox && span) {
                    console.log(`Amenity ${index}: "${span.textContent.trim()}" - Checked: ${checkbox.checked}`);
                    if (checkbox.checked) {
                        amenities.basic.push(span.textContent.trim());
                    }
                }
            });
        } else {
            console.log('❌ Basic amenities container not found');
        }

        // Collect appliances
        const appliancesContainer = document.getElementById('appliances-list');
        console.log('Appliances container:', appliancesContainer);

        if (appliancesContainer) {
            const applianceElements = appliancesContainer.querySelectorAll('div.p-3');
            console.log(`Found ${applianceElements.length} appliance elements`);

            applianceElements.forEach((element, index) => {
                const inputs = element.querySelectorAll('input[type="text"]');
                console.log(`Appliance ${index}: Found ${inputs.length} input fields`);

                if (inputs.length >= 4) {
                    const name = inputs[0].value.trim();
                    const location = inputs[1].value.trim();
                    const brand = inputs[2].value.trim();
                    const model = inputs[3].value.trim();

                    console.log(`Appliance ${index} data: Name="${name}", Location="${location}", Brand="${brand}", Model="${model}"`);

                    if (name) { // Only include if name is provided
                        amenities.appliances.push({
                            name: name,
                            location: location,
                            brand: brand,
                            model: model
                        });
                    }
                }
            });
        } else {
            console.log('❌ Appliances container not found');
        }

        console.log('Collected current amenities:', amenities);
        return amenities;
    }

    saveStepToServer(step, data) {
        // Save step data to server
        return fetch(`/api/properties/${this.propertyId}/setup-progress`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                step: step,
                data: data
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                console.log(`Step ${step} saved successfully`);
                // Update local property data with saved changes
                this.updateLocalPropertyData(step, data);
                return true;
            } else {
                console.error(`Failed to save step ${step}:`, result.error);
                return false;
            }
        })
        .catch(error => {
            console.error('Error saving step data:', error);
            return false;
        });
    }

    updateLocalPropertyData(step, data) {
        // Update the local property data with the saved changes
        if (step === 1) { // Basic Information
            this.propertyData.name = data.name || this.propertyData.name;
            this.propertyData.address = data.address || this.propertyData.address;
            this.propertyData.description = data.description || this.propertyData.description;
            this.propertyData.icalUrl = data.icalUrl || this.propertyData.icalUrl;
            this.propertyData.checkInTime = data.checkInTime || this.propertyData.checkInTime;
            this.propertyData.checkOutTime = data.checkOutTime || this.propertyData.checkOutTime;
            this.propertyData.wifiDetails = data.wifiDetails || this.propertyData.wifiDetails;

            // Update amenities if provided
            if (data.amenities) {
                this.propertyData.amenities = data.amenities;
                console.log('Updated amenities in local property data:', data.amenities);
            }
        } else if (step === 2) {
            // Update house rules if provided
            if (data.houseRules) {
                this.propertyData.houseRules = data.houseRules;
                console.log('Updated house rules in local property data:', data.houseRules);
            }
        } else if (step === 3) {
            // Update emergency information if provided
            if (data.emergencyInfo) {
                this.propertyData.emergencyInfo = data.emergencyInfo;
                console.log('Updated emergency info in local property data:', data.emergencyInfo);
            }
        } else if (step === 4) {
            // Update property facts if provided
            if (data.propertyFacts) {
                this.propertyData.propertyFacts = data.propertyFacts;
                console.log('Updated property facts in local property data:', data.propertyFacts);
            }
        }

        console.log('Updated local property data:', this.propertyData);
    }

    async nextStep() {
        if (this.currentStep < this.totalSteps) {
            const saved = await this.saveCurrentStepData();
            if (saved) {
                this.loadStep(this.currentStep + 1);
            } else {
                console.error('Failed to save current step data');
                // Show user feedback for validation errors
                this.showStepError('Please fill in all required fields before proceeding to the next step.');
            }
        } else {
            // Complete setup
            this.completeSetup();
        }
    }

    async previousStep() {
        if (this.currentStep > 1) {
            await this.saveCurrentStepData();
            this.loadStep(this.currentStep - 1);
        }
    }

    updateProgressBar() {
        const modal = document.getElementById('property-setup-modal');
        if (modal) {
            // Update progress bar and step indicators
            const progressBar = modal.querySelector('.bg-white.rounded-full.h-2');
            if (progressBar) {
                progressBar.style.width = `${(this.currentStep / this.totalSteps) * 100}%`;
            }

            // Update step counter and percentage in header
            const stepCounter = modal.querySelector('.flex.items-center.justify-between.text-sm.mb-2.text-white span:first-child');
            if (stepCounter) {
                stepCounter.textContent = `Step ${this.currentStep} of ${this.totalSteps}`;
            }

            const percentageText = modal.querySelector('.flex.items-center.justify-between.text-sm.mb-2.text-white span:last-child');
            if (percentageText) {
                percentageText.textContent = `${Math.round((this.currentStep / this.totalSteps) * 100)}% Complete`;
            }

            // Update step text
            const stepTexts = modal.querySelectorAll('.text-xs.mt-2.text-white span');
            stepTexts.forEach((span, index) => {
                if (index + 1 <= this.currentStep) {
                    span.className = 'font-medium opacity-100';
                } else {
                    span.className = 'opacity-80';
                }
            });
        }
    }

    updateNavigationButtons() {
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');

        if (prevBtn) {
            prevBtn.disabled = this.currentStep === 1;
            prevBtn.className = `px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors ${this.currentStep === 1 ? 'opacity-50 cursor-not-allowed' : ''}`;
        }

        if (nextBtn) {
            nextBtn.innerHTML = `
                <span class="btn-text">${this.currentStep === this.totalSteps ? 'Complete Setup' : 'Next'}</span>
                <i class="fas ${this.currentStep === this.totalSteps ? 'fa-check' : 'fa-arrow-right'} ml-2"></i>
            `;

            // Reset button state for non-final steps
            if (this.currentStep !== this.totalSteps) {
                nextBtn.disabled = false;
                nextBtn.className = 'px-4 py-2 bg-persian-green text-white rounded-lg hover:bg-persian-green/90 transition-colors';
            }
        }

        // Update step indicator text in footer - use specific ID
        const stepIndicator = document.getElementById('step-indicator');
        console.log('Step indicator element:', stepIndicator);
        console.log('Current step:', this.currentStep, 'Step name:', this.stepNames[this.currentStep - 1]);
        if (stepIndicator) {
            const newText = `Step ${this.currentStep} of ${this.totalSteps}: ${this.stepNames[this.currentStep - 1]}`;
            console.log('Updating step indicator to:', newText);
            stepIndicator.textContent = newText;
        } else {
            console.log('Step indicator not found by ID');
        }
    }

    completeSetup() {
        // Show loading state
        const nextBtn = document.getElementById('next-btn');
        if (nextBtn) {
            nextBtn.disabled = true;
            nextBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i><span class="btn-text">Completing Setup...</span>';
        }

        // Prepare all setup data for submission
        const setupPayload = {
            basicInfo: this.setupData.basicInfo,
            houseRules: this.setupData.houseRules,
            emergencyInfo: this.setupData.emergencyInfo,
            propertyFacts: this.setupData.propertyFacts
        };

        // Complete setup with all knowledge data
        fetch(`/api/properties/${this.propertyId}/complete-setup`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify(setupPayload)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                this.close();
                // Refresh the properties list
                if (typeof loadProperties === 'function') {
                    loadProperties();
                }
                // Show success message
                showSuccessMessage('Property setup completed successfully! All knowledge has been saved, and AI assistant is ready to help your guests.');
            } else {
                showErrorMessage(result.error || 'Failed to complete setup');
                // Restore button state
                if (nextBtn) {
                    nextBtn.disabled = false;
                    nextBtn.innerHTML = '<span class="btn-text">Complete Setup</span> <i class="fas fa-check ml-2"></i>';
                }
            }
        })
        .catch(error => {
            console.error('Error completing setup:', error);
            showErrorMessage('Failed to complete setup');
            // Restore button state
            if (nextBtn) {
                nextBtn.disabled = false;
                nextBtn.innerHTML = '<span class="btn-text">Complete Setup</span> <i class="fas fa-check ml-2"></i>';
            }
        });
    }

    close() {
        // Clean up resize listener
        if (this.resizeListener) {
            window.removeEventListener('resize', this.resizeListener);
            this.resizeListener = null;
        }

        const modal = document.getElementById('property-setup-modal');
        if (modal) {
            modal.remove();
            document.body.style.overflow = 'auto';
        }
    }

    // House Rules step implementation
    loadHouseRulesStep(content) {
        content.innerHTML = `
            <div>
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-dark-purple mb-2">
                        <i class="fas fa-gavel text-persian-green mr-2"></i>
                        House Rules
                    </h4>
                    <p class="text-gray-600">Set clear expectations for your guests. Enable the rules that apply to your property and customize them as needed. Make sure it matches what you have in your listing. Remember, changing your house rules here will not update the rules in your listing.</p>
                </div>

                <!-- Unified House Rules Content -->
                <div id="house-rules-content">
                    <!-- Will be populated by loadUnifiedRules() -->
                    <div class="text-center py-8">
                        <div class="loading-spinner mx-auto mb-4"></div>
                        <p class="text-gray-600">Loading house rules...</p>
                    </div>
                </div>
            </div>
        `;

        // Load unified rules section
        this.loadUnifiedRules();
    }

    loadEmergencyInformationStep(content) {
        content.innerHTML = `
            <div>
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-dark-purple mb-2">
                        <i class="fas fa-exclamation-triangle text-persian-green mr-2"></i>
                        Emergency Information
                    </h4>
                    <p class="text-gray-600">Provide essential emergency information for your guests. Enable and customize the information that applies to your property.</p>
                </div>

                <!-- Emergency Information Content -->
                <div id="emergency-info-content">
                    <!-- Will be populated by loadEmergencyInformation() -->
                    <div class="text-center py-8">
                        <div class="loading-spinner mx-auto mb-4"></div>
                        <p class="text-gray-600">Loading emergency information...</p>
                    </div>
                </div>
            </div>
        `;

        // Load emergency information
        this.loadEmergencyInformation();
    }

    loadPropertyFactsStep(content) {
        // Questions from CSV, excluding those covered in previous steps and Emergency Info
        // Removed: water main shut off, water heater, electrical panel, first-aid kit (covered in Emergency Info)
        // Removed: emergency procedures, injury response (covered in Emergency Info)
        const propertyFactsQuestions = [
            // Location and Accessibility
            "Closest gas station",
            "Where's the nearest pharmacy/hospital?",
            "Are there any stairs in the property?",
            "What's the noise level like in the area?",
            "Is there a cell phone signal?",

            // Guest Convenience
            "Where are the extra towels/linens?",
            "Where are extra blankets/lines/comforters?",
            "Where do I put the trash/recycling? When is trash pickup?",
            "Do you provide toiletries/basic supplies (e.g., toilet paper, paper towels, dish soap)?",
            "Where's the dishwasher detergent?",
            "Where are the cleaning supplies?/broom/vac",
            "What's the bedding configuration in each room?",
            "Is there a specific checkout procedure (e.g., strip beds, start laundry)?",
            "Can you hold our luggage after check-out?",

            // Local Recommendations
            "Any local numbers/recs you want to share",
            "Can you recommend local restaurants/cafes?",
            "What are some popular local attractions/things to do nearby?",
            "Is there a grocery store nearby?",
            "What's the best way to get around (public transport, taxis, ride-sharing)?",
            "Local recommendation for family fun",

            // Property Specifics
            "Any things I should be careful about?",
            "What sort of wild animals might we see? (Common for rural/nature-based rentals)",
            "Can we use the water from the taps for drinking/cooking? (More common in places with well water or specific water advisories.)",
            "What is the maximum occupancy?",
            "Do you offer discounts for longer stays/return visits?",
            "Do you have a crib/pack-n-play or high chair available?",
            "Are there sports channels on the TV?",
            "Is there a fireplace/fire pit? How do I use it?"
        ];

        content.innerHTML = `
            <div>
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-dark-purple mb-2">
                        <i class="fas fa-clipboard-list text-persian-green mr-2"></i>
                        Other Information
                    </h4>
                    <p class="text-gray-600 mb-4">
                        Answer the questions below to create comprehensive knowledge about your property so AI assistant can cover guest queries beyond amenities and areas covered before.
                        <strong>None of these questions are mandatory</strong> - provide answers that you feel would be helpful for this specific property.
                    </p>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-2"></i>
                            <div class="text-sm text-blue-700">
                                <strong>Tip:</strong> The more detailed information you provide, the better AI assistant can help your guests with specific questions about your property.
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-6" id="property-facts-form">
                    ${propertyFactsQuestions.map((question, index) => `
                        <div class="bg-gray-50 p-4 rounded-lg property-fact-item" data-type="default">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                ${question}
                            </label>
                            <textarea
                                id="fact-${index}"
                                name="fact-${index}"
                                data-question="${question}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green resize-none"
                                rows="2"
                                placeholder="Enter your answer (optional)..."
                            ></textarea>
                        </div>
                    `).join('')}
                </div>

                <!-- Add Custom Property Fact Section -->
                <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h6 class="font-medium text-gray-900 mb-3">Add Custom Property Fact</h6>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Property Fact</label>
                            <textarea id="custom-fact-content"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green resize-none"
                                      rows="3"
                                      placeholder="Enter a useful fact about your property (e.g., 'The WiFi password is written on the router', 'Extra towels are in the hall closet')..."></textarea>
                        </div>
                        <button onclick="propertySetupModal.addCustomPropertyFact()"
                                class="inline-flex items-center px-4 py-2 border border-persian-green text-persian-green rounded-lg hover:bg-persian-green hover:text-white transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Add This Fact
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Load existing property facts if any
        this.loadExistingPropertyFacts();

        // Add auto-save listeners
        this.addPropertyFactsAutoSave();

        // Add Enter key support for custom fact inputs
        this.addCustomFactKeyListeners();
    }

    loadExistingPropertyFacts() {
        // Load existing property facts from setupData or propertyData
        if (this.setupData.propertyFacts && this.setupData.propertyFacts.length > 0) {
            const form = document.getElementById('property-facts-form');

            this.setupData.propertyFacts.forEach((fact, index) => {
                const textarea = document.querySelector(`textarea[data-question="${fact.question}"]`);
                if (textarea) {
                    // Load answer for existing predefined questions
                    textarea.value = fact.answer || '';
                } else {
                    // This is a custom fact that needs to be recreated

                    const factItems = form.querySelectorAll('.property-fact-item');
                    const newIndex = factItems.length;

                    const newFactHtml = `
                        <div class="bg-gray-50 p-4 rounded-lg property-fact-item group" data-type="custom">
                            <div class="flex justify-between items-start mb-2">
                                <label class="block text-sm font-medium text-gray-700 flex-1">
                                    ${fact.question}
                                </label>
                                <button onclick="propertySetupModal.removePropertyFact(${newIndex})"
                                        class="text-red-500 hover:text-red-700 ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <textarea
                                id="fact-${newIndex}"
                                name="fact-${newIndex}"
                                data-question="${fact.question}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green resize-none"
                                rows="2"
                                placeholder="Enter your answer (optional)..."
                            >${fact.answer || ''}</textarea>
                        </div>
                    `;

                    form.insertAdjacentHTML('beforeend', newFactHtml);

                    // Add auto-save listener to the new textarea
                    const newTextarea = document.getElementById(`fact-${newIndex}`);
                    if (newTextarea) {
                        newTextarea.addEventListener('input', () => {
                            clearTimeout(this.propertyFactsAutoSaveTimeout);
                            this.propertyFactsAutoSaveTimeout = setTimeout(() => {
                                this.savePropertyFactsData();
                            }, 1000);
                        });
                    }
                }
            });
        }
    }

    addPropertyFactsAutoSave() {
        const textareas = document.querySelectorAll('#property-facts-form textarea');
        textareas.forEach(textarea => {
            textarea.addEventListener('input', () => {
                // Debounced auto-save
                clearTimeout(this.propertyFactsAutoSaveTimeout);
                this.propertyFactsAutoSaveTimeout = setTimeout(() => {
                    this.savePropertyFactsData();
                }, 1000);
            });
        });
    }

    savePropertyFactsData() {
        const textareas = document.querySelectorAll('#property-facts-form textarea');
        const propertyFacts = [];

        textareas.forEach((textarea, index) => {
            const question = textarea.dataset.question;
            const answer = textarea.value.trim();

            if (answer) { // Only save non-empty answers
                propertyFacts.push({
                    question: question,
                    answer: answer
                });
            }
        });

        this.setupData.propertyFacts = propertyFacts;
        console.log('Property facts data saved:', propertyFacts);

        // Also save to server immediately
        this.saveStepToServer(4, { propertyFacts: propertyFacts }).catch(error => {
            console.error('Error auto-saving property facts to server:', error);
        });
    }

    async savePropertyFacts() {
        this.savePropertyFactsData();

        // Property facts are optional, so always return true for validation
        // But still save to server for persistence
        const propertyFacts = this.setupData.propertyFacts || [];

        try {
            await this.saveStepToServer(4, { propertyFacts: propertyFacts });
            console.log('Property facts saved successfully');
        } catch (error) {
            console.error('Error saving property facts:', error);
            // Don't block progression even if server save fails
        }

        return true; // Always allow progression since facts are optional
    }

    addCustomPropertyFact() {
        const contentInput = document.getElementById('custom-fact-content');
        const content = contentInput?.value?.trim() || '';

        if (!content) {
            alert('Please enter a property fact before adding it.');
            contentInput?.focus();
            return;
        }

        // Add to the form
        const form = document.getElementById('property-facts-form');
        const factItems = form.querySelectorAll('.property-fact-item');
        const newIndex = factItems.length;

        // Use the content as both question and answer for consistency with existing structure
        const factQuestion = `Custom Fact ${newIndex + 1}`;

        const newFactHtml = `
            <div class="bg-green-50 p-4 rounded-lg property-fact-item group border border-green-200" data-type="custom">
                <div class="flex justify-between items-start mb-2">
                    <label class="block text-sm font-medium text-green-800 flex items-center">
                        <i class="fas fa-plus-circle text-green-600 mr-2"></i>
                        ${factQuestion}
                    </label>
                    <button onclick="propertySetupModal.removePropertyFact(${newIndex})"
                            class="text-red-600 hover:text-red-800 opacity-0 group-hover:opacity-100 transition-opacity"
                            title="Remove this fact">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
                <textarea
                    id="fact-${newIndex}"
                    name="fact-${newIndex}"
                    data-question="${factQuestion}"
                    class="w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-green-500 focus:border-green-500 resize-none bg-white"
                    rows="2"
                    placeholder="Property fact..."
                >${content}</textarea>
            </div>
        `;

        form.insertAdjacentHTML('beforeend', newFactHtml);

        // Clear the input field
        contentInput.value = '';

        // Add auto-save listener to the new textarea
        const newTextarea = document.getElementById(`fact-${newIndex}`);
        if (newTextarea) {
            newTextarea.addEventListener('input', () => {
                clearTimeout(this.propertyFactsAutoSaveTimeout);
                this.propertyFactsAutoSaveTimeout = setTimeout(() => {
                    this.savePropertyFactsData();
                }, 1000);
            });
        }

        // Save the data immediately
        this.savePropertyFactsData();

        // Focus back on content input for next entry
        contentInput?.focus();

        console.log('Added custom property fact:', content);
    }

    addCustomFactKeyListeners() {
        const questionInput = document.getElementById('custom-fact-question');
        const answerInput = document.getElementById('custom-fact-answer');

        if (questionInput) {
            questionInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    answerInput?.focus();
                }
            });
        }

        if (answerInput) {
            answerInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.addCustomPropertyFact();
                }
            });
        }
    }

    removePropertyFact(index) {
        const factItems = document.querySelectorAll('.property-fact-item');
        if (factItems[index]) {
            // Only allow removal of custom facts
            if (factItems[index].dataset.type === 'custom') {
                factItems[index].remove();
                this.savePropertyFactsData();
            }
        }
    }

    loadReviewAndApproveStep(content) {
        content.innerHTML = `
            <div>
                <div class="mb-6">
                    <h4 class="text-lg font-semibold text-dark-purple mb-2">
                        <i class="fas fa-check-circle text-persian-green mr-2"></i>
                        Review and Approve
                    </h4>
                    <p class="text-gray-600">Review all the information you've provided and submit to finish your property setup. This is the time to make sure all the data is 100% correct and complete.</p>
                </div>

                <div class="space-y-6" id="review-sections">
                    <!-- Review sections will be loaded here -->
                </div>

                <div class="mt-8 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-green-500 mt-0.5 mr-3"></i>
                        <div class="text-sm text-green-700">
                            <strong>Ready to complete setup?</strong><br>
                            All the knowledge you've provided will be saved and made available to AI assistant to help assist your guests.
                            You can always edit this information later from your property management dashboard.
                        </div>
                    </div>

                    <!-- Verification Checkbox -->
                    <div class="mt-4 pt-4 border-t border-green-200">
                        <label class="flex items-start cursor-pointer">
                            <input type="checkbox"
                                   id="data-verification-checkbox"
                                   class="mt-1 mr-3 h-4 w-4 text-persian-green border-gray-300 rounded focus:ring-persian-green"
                                   onchange="propertySetupModal.toggleCompleteButton()">
                            <span class="text-sm text-green-700">
                                <strong>I have reviewed and verified that all the data in every section above is 100% accurate and complete.</strong>
                                I understand this information will be used by the AI assistant to help my guests.
                            </span>
                        </label>
                    </div>
                </div>
            </div>
        `;

        this.loadReviewSections();

        // Initially disable the Complete Setup button
        this.toggleCompleteButton();
    }

    toggleCompleteButton() {
        // Only apply validation logic on step 5 (Review step)
        if (this.currentStep !== this.totalSteps) {
            return;
        }

        const checkbox = document.getElementById('data-verification-checkbox');
        const nextBtn = document.getElementById('next-btn');

        if (checkbox && nextBtn) {
            const isChecked = checkbox.checked;
            nextBtn.disabled = !isChecked;

            if (isChecked) {
                nextBtn.className = 'px-4 py-2 bg-persian-green text-white rounded-lg hover:bg-persian-green/90 transition-colors';
            } else {
                nextBtn.className = 'px-4 py-2 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed';
            }
        }
    }

    loadReviewSections() {
        const container = document.getElementById('review-sections');
        if (!container) return;

        let html = '';

        // Section 1: Basic Information
        html += this.generateReviewSection(
            1,
            'Basic Information',
            'fas fa-info-circle',
            this.getBasicInfoSummary()
        );

        // Section 2: House Rules
        html += this.generateReviewSection(
            2,
            'House Rules',
            'fas fa-gavel',
            this.getHouseRulesSummary()
        );

        // Section 3: Emergency Information
        html += this.generateReviewSection(
            3,
            'Emergency Information',
            'fas fa-exclamation-triangle',
            this.getEmergencyInfoSummary()
        );

        // Section 4: Other Information
        html += this.generateReviewSection(
            4,
            'Other Information',
            'fas fa-clipboard-list',
            this.getPropertyFactsSummary()
        );

        container.innerHTML = html;
    }

    generateReviewSection(stepNumber, title, icon, summary) {
        return `
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h5 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="${icon} text-persian-green mr-2"></i>
                        ${title}
                    </h5>
                    <button onclick="propertySetupModal.editStep(${stepNumber})"
                            class="px-3 py-1 text-sm border border-persian-green text-persian-green rounded hover:bg-persian-green hover:text-white transition-colors">
                        <i class="fas fa-edit mr-1"></i>Edit
                    </button>
                </div>
                <div class="text-gray-600 text-sm">
                    ${summary}
                </div>
            </div>
        `;
    }

    getBasicInfoSummary() {
        // Always use propertyData as the source of truth since it contains the most up-to-date info
        const basicInfo = this.propertyData;

        console.log('Getting basic info summary from propertyData:', basicInfo);
        console.log('Property data keys:', Object.keys(basicInfo || {}));
        console.log('Property name:', basicInfo?.name);
        console.log('Property address:', basicInfo?.address);

        if (!basicInfo || Object.keys(basicInfo).length === 0) {
            console.log('No basic info found, returning empty message');
            return '<p class="text-gray-500 italic">No basic information provided yet.</p>';
        }

        let summary = '';

        // Basic property details
        if (basicInfo.name) summary += `<strong>Property Name:</strong> ${basicInfo.name}<br>`;
        if (basicInfo.address) summary += `<strong>Address:</strong> ${basicInfo.address}<br>`;
        if (basicInfo.description) summary += `<strong>Description:</strong> ${basicInfo.description}<br>`;
        if (basicInfo.icalUrl) summary += `<strong>Calendar URL:</strong> Connected<br>`;
        if (basicInfo.checkInTime) summary += `<strong>Check-in Time:</strong> ${basicInfo.checkInTime}<br>`;
        if (basicInfo.checkOutTime) summary += `<strong>Check-out Time:</strong> ${basicInfo.checkOutTime}<br>`;

        // WiFi details
        if (basicInfo.wifiDetails?.network) {
            summary += `<strong>WiFi Details:</strong> Network: ${basicInfo.wifiDetails.network}`;
            if (basicInfo.wifiDetails?.password) {
                summary += `, Password: ${basicInfo.wifiDetails.password}`;
            }
            summary += '<br>';
        }

        // Amenities details
        const amenities = basicInfo.amenities;
        if (amenities) {
            // Basic amenities
            if (amenities.basic && amenities.basic.length > 0) {
                summary += `<strong>Basic Amenities:</strong> ${amenities.basic.join(', ')}<br>`;
            }

            // Appliances
            if (amenities.appliances && amenities.appliances.length > 0) {
                summary += '<strong>Appliances:</strong><br>';
                amenities.appliances.forEach(appliance => {
                    summary += `&nbsp;&nbsp;• ${appliance.name}`;
                    if (appliance.location) summary += ` (${appliance.location})`;
                    if (appliance.brand || appliance.model) {
                        const brandModel = [appliance.brand, appliance.model].filter(Boolean).join(' ');
                        if (brandModel) summary += ` - ${brandModel}`;
                    }
                    summary += '<br>';
                });
            }
        }

        return summary || '<p class="text-gray-500 italic">No basic information provided yet.</p>';
    }

    getHouseRulesSummary() {
        const houseRules = this.setupData.houseRules;
        if (!houseRules || houseRules.length === 0) {
            return '<p class="text-gray-500 italic">No house rules configured.</p>';
        }

        const enabledRules = houseRules.filter(rule => rule.enabled);
        if (enabledRules.length === 0) {
            return '<p class="text-gray-500 italic">No house rules enabled.</p>';
        }

        let summary = `<strong>${enabledRules.length} house rules active:</strong><br><br>`;
        enabledRules.forEach(rule => {
            summary += `<strong>${rule.title}:</strong> ${rule.content || rule.description || 'No details provided'}<br><br>`;
        });

        return summary;
    }

    getEmergencyInfoSummary() {
        const emergencyInfo = this.setupData.emergencyInfo;
        if (!emergencyInfo || emergencyInfo.length === 0) {
            return '<p class="text-gray-500 italic">No emergency information configured.</p>';
        }

        const enabledInfo = emergencyInfo.filter(info => info.enabled);
        if (enabledInfo.length === 0) {
            return '<p class="text-gray-500 italic">No emergency information enabled.</p>';
        }

        let summary = `<strong>${enabledInfo.length} emergency procedures configured:</strong><br><br>`;
        enabledInfo.forEach(info => {
            summary += `<strong>${info.title}:</strong><br>`;
            if (info.instructions) {
                summary += `${info.instructions}<br>`;
            }
            if (info.location) {
                summary += `<em>Location: ${info.location}</em><br>`;
            }
            summary += '<br>';
        });

        return summary;
    }

    getPropertyFactsSummary() {
        const propertyFacts = this.setupData.propertyFacts;

        if (!propertyFacts || propertyFacts.length === 0) {
            return '<p class="text-gray-500 italic">No other information provided.</p>';
        }

        const answeredFacts = propertyFacts.filter(fact => fact.answer && fact.answer.trim());

        if (answeredFacts.length === 0) {
            return '<p class="text-gray-500 italic">No other information answered.</p>';
        }

        let summary = `<strong>${answeredFacts.length} other information items provided:</strong><br><br>`;
        summary += '<div class="space-y-2">';
        answeredFacts.forEach(fact => {
            // Show the answer content that will be fed to AI
            const answer = fact.answer.trim();
            const shortAnswer = answer.length > 100 ? answer.substring(0, 100) + '...' : answer;
            summary += `<div class="bg-gray-50 p-2 rounded text-sm">`;
            if (fact.question) {
                summary += `<strong>Q:</strong> ${fact.question}<br>`;
            }
            summary += `<strong>A:</strong> ${shortAnswer}`;
            summary += `</div>`;
        });
        summary += '</div>';

        return summary;
    }

    editStep(stepNumber) {
        // Save current step data before navigating
        this.saveCurrentStepData().then(() => {
            this.loadStep(stepNumber);
        });
    }

    async saveHouseRules() {
        console.log('Saving unified house rules...');

        // Collect all enabled rules from the unified section
        const allRules = [];
        const container = document.getElementById('house-rules-content');

        if (container && this.unifiedRulesData) {
            const checkboxes = container.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach((checkbox, index) => {
                if (checkbox.checked && this.unifiedRulesData[index]) {
                    const descTextarea = document.getElementById(`rule_desc_${index}`);
                    const description = descTextarea ? descTextarea.value : '';

                    const rule = {
                        ...this.unifiedRulesData[index],
                        description: description,
                        enabled: true
                    };

                    allRules.push(rule);
                }
            });
        }

        // Save to setup data
        this.setupData.houseRules = allRules;

        console.log('House rules to save:', allRules);

        // Save to server
        const saved = await this.saveStepToServer(2, { houseRules: allRules });
        return saved;
    }



    async loadEmergencyInformation() {
        console.log('Loading emergency information...');

        // Check if we already have saved emergency info from user modifications
        if (this.setupData.emergencyInfo && this.setupData.emergencyInfo.length > 0) {
            console.log('✅ Using existing saved emergency info:', this.setupData.emergencyInfo.length);

            // Use the saved emergency info directly, but we need to reconstruct the full list
            // to maintain the UI structure for items that were disabled
            const defaultEmergencyInfo = this.getDefaultEmergencyInfo();
            const importedEmergencyInfo = this.getImportedEmergencyInfo();
            let allEmergencyInfo = this.mergeEmergencyInfo(defaultEmergencyInfo, importedEmergencyInfo);

            // Update the enabled status and content based on saved data
            this.setupData.emergencyInfo.forEach(savedItem => {
                const existingIndex = allEmergencyInfo.findIndex(item => item.id === savedItem.id);
                if (existingIndex >= 0) {
                    // Update existing item with saved data
                    allEmergencyInfo[existingIndex] = {
                        ...allEmergencyInfo[existingIndex],
                        ...savedItem,
                        enabled: true // Items in setupData.emergencyInfo are enabled
                    };
                } else {
                    // Add custom item
                    allEmergencyInfo.push({
                        ...savedItem,
                        enabled: true
                    });
                }
            });

            // Mark all other items as disabled
            allEmergencyInfo.forEach(item => {
                const isSaved = this.setupData.emergencyInfo.some(saved => saved.id === item.id);
                if (!isSaved) {
                    item.enabled = false;
                }
            });

            this.renderEmergencyInformation(allEmergencyInfo);
            return;
        }

        // If no saved emergency info, load from original sources
        this.loadOriginalEmergencyInformation();
    }

    async loadOriginalEmergencyInformation() {
        console.log('Loading original emergency information from import data...');

        // Get default emergency information items
        const defaultEmergencyInfo = this.getDefaultEmergencyInfo();

        // Check for any imported emergency information
        const importedEmergencyInfo = this.getImportedEmergencyInfo();

        // Start with defaults and imported items
        let allEmergencyInfo = this.mergeEmergencyInfo(defaultEmergencyInfo, importedEmergencyInfo);

        // If we have current emergency info from previous saves, merge it in
        if (this.currentEmergencyInfo && this.currentEmergencyInfo.length > 0) {
            console.log('Merging existing emergency info from current session:', this.currentEmergencyInfo);

            // Update the merged info with current session data
            this.currentEmergencyInfo.forEach(currentItem => {
                const existingIndex = allEmergencyInfo.findIndex(item => item.id === currentItem.id);
                if (existingIndex >= 0) {
                    // Update existing item
                    allEmergencyInfo[existingIndex] = currentItem;
                } else {
                    // Add new custom item
                    allEmergencyInfo.push(currentItem);
                }
            });
        }

        this.renderEmergencyInformation(allEmergencyInfo);
    }

    getDefaultEmergencyInfo() {
        return [
            {
                id: 'gas_leak',
                title: 'Gas Leak',
                instructions: 'Do not use electrical switches or open flames. Evacuate immediately and call gas company emergency line.',
                location: 'Gas shut-off valve location: ',
                enabled: false,
                type: 'default'
            },
            {
                id: 'water_leak',
                title: 'Water Leak',
                instructions: 'Turn off main water supply immediately. Contact property manager or emergency plumber.',
                location: 'Main water shut-off location: ',
                enabled: false,
                type: 'default'
            },
            {
                id: 'power_outage',
                title: 'Power Outage',
                instructions: 'Check circuit breaker first. If widespread outage, contact utility company.',
                location: 'Circuit breaker location: ',
                enabled: false,
                type: 'default'
            },
            {
                id: 'lockout',
                title: 'Lockout',
                instructions: 'Contact property manager or host immediately. Do not attempt to force entry.',
                location: '',
                enabled: false,
                type: 'default'
            },
            {
                id: 'severe_weather',
                title: 'Severe Weather',
                instructions: 'Stay indoors. Monitor local weather alerts. Know the location of safe areas.',
                location: 'Safe area location: ',
                enabled: false,
                type: 'default'
            },
            {
                id: 'carbon_monoxide',
                title: 'Carbon Monoxide Alarm',
                instructions: 'Evacuate immediately. Do not re-enter until cleared by professionals. Call 911.',
                location: '',
                enabled: false,
                type: 'default'
            },
            {
                id: 'first_aid',
                title: 'First Aid Kit',
                instructions: 'Basic first aid supplies for minor injuries.',
                location: 'First aid kit location: ',
                enabled: false,
                type: 'default'
            },
            {
                id: 'fire_extinguisher',
                title: 'Fire Extinguisher',
                instructions: 'For small fires only. Pull pin, aim at base of fire, squeeze handle, sweep side to side.',
                location: 'Fire extinguisher location: ',
                enabled: false,
                type: 'default'
            },
            {
                id: 'emergency_contacts',
                title: 'Emergency Contacts',
                instructions: 'Property Manager: [Phone]\nLocal Emergency: 911\nPoison Control: **************',
                location: '',
                enabled: false,
                type: 'default'
            }
        ];
    }

    getImportedEmergencyInfo() {
        // Check for imported emergency information from property data
        const importedSafety = this.propertyData.importData?.rawData?.extracted?.safety_info || [];
        const deepExtractedSafety = this.propertyData.importData?.rawData?.safety_info || [];
        const allImportedSafety = [...importedSafety, ...deepExtractedSafety];

        console.log('Imported safety info:', allImportedSafety);

        return allImportedSafety.map((item, index) => ({
            id: `imported_${index}`,
            title: item.title || item.type || 'Safety Information',
            instructions: item.description || item.instructions || '',
            location: item.location || '',
            enabled: true, // Enable imported items by default
            type: 'imported'
        }));
    }

    mergeEmergencyInfo(defaultInfo, importedInfo) {
        // Start with imported info (enabled by default)
        const merged = [...importedInfo];

        // Add any existing custom items from previous saves
        const existingCustomItems = this.propertyData.emergencyInfo?.filter(item => item.type === 'custom') || [];
        merged.push(...existingCustomItems);

        // Add default info that doesn't conflict
        defaultInfo.forEach(defaultItem => {
            const hasConflict = importedInfo.some(imported =>
                imported.title.toLowerCase().includes(defaultItem.title.toLowerCase()) ||
                defaultItem.title.toLowerCase().includes(imported.title.toLowerCase())
            );

            if (!hasConflict) {
                merged.push(defaultItem);
            }
        });

        return merged;
    }

    renderEmergencyInformation(emergencyInfo) {
        const container = document.getElementById('emergency-info-content');
        if (!container) return;

        // Store emergency info for reference
        this.currentEmergencyInfo = emergencyInfo;

        let html = '';

        // Add help text at the top
        html += `
            <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-start space-x-2">
                    <i class="fas fa-info-circle text-blue-600 mt-0.5"></i>
                    <div class="text-sm text-blue-800">
                        <p class="font-medium mb-1">Emergency Information Tips:</p>
                        <ul class="list-disc list-inside space-y-1 text-blue-700">
                            <li>Only enable information that applies to your property</li>
                            <li>Customize instructions to be specific to your location</li>
                            <li>Include exact locations for safety equipment and shut-offs</li>
                            <li>Keep emergency contact information up to date</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;

        // Separate default/imported items from custom items
        const defaultAndImportedItems = emergencyInfo.filter(info => info.type !== 'custom');
        const customItems = emergencyInfo.filter(info => info.type === 'custom');

        if (defaultAndImportedItems.length === 0 && customItems.length === 0) {
            html += `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-info-circle text-4xl mb-4"></i>
                    <p>No emergency information available. You can enable and customize the default options below.</p>
                </div>
            `;
        } else {
            // Render default and imported items first
            if (defaultAndImportedItems.length > 0) {
                html += '<div class="space-y-4" id="emergency-items-container">';

                defaultAndImportedItems.forEach((info, originalIndex) => {
                    // Find the original index in the full array
                    const index = emergencyInfo.findIndex(item => item.id === info.id);
                    html += this.generateEmergencyItemHTML(info, index);
                });

                html += '</div>';
            }

            // Render custom items at the bottom
            if (customItems.length > 0) {
                html += `
                    <div class="mt-8">
                        <h5 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-plus-circle text-green-600 mr-2"></i>
                            Custom Emergency Items
                        </h5>
                        <div class="space-y-4" id="custom-emergency-items-container">
                `;

                customItems.forEach((info, originalIndex) => {
                    // Find the original index in the full array
                    const index = emergencyInfo.findIndex(item => item.id === info.id);
                    html += this.generateEmergencyItemHTML(info, index);
                });

                html += '</div></div>';
            }
        }

        // Add "Add Custom Emergency Item" button at the bottom
        html += `
            <div class="mt-6 mb-6">
                <button onclick="propertySetupModal.addCustomEmergencyItem()"
                        class="inline-flex items-center px-4 py-2 border border-persian-green text-persian-green rounded-lg hover:bg-persian-green hover:text-white transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Add Custom Emergency Item
                </button>
            </div>
        `;

        container.innerHTML = html;

        // Add dynamic saving event listeners
        this.addEmergencyInfoEventListeners();
    }

    generateEmergencyItemHTML(info, index) {
        const isImported = info.type === 'imported';
        const isCustom = info.type === 'custom';
        const sourceClass = isImported ? 'bg-blue-100 text-blue-800' :
                           isCustom ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
        const sourceLabel = isImported ? 'Imported' : isCustom ? 'Custom' : 'Default';
        const borderClass = isImported ? 'border-blue-200 bg-blue-50' :
                           isCustom ? 'border-green-200 bg-green-50' : 'border-gray-200';

        return `
            <div class="border rounded-lg p-4 ${borderClass}" data-emergency-index="${index}">
                <div class="flex items-start space-x-3">
                    <input type="checkbox"
                           id="emergency_${index}"
                           data-emergency-id="${info.id}"
                           ${info.enabled ? 'checked' : ''}
                           class="mt-1 h-4 w-4 text-persian-green border-gray-300 rounded focus:ring-persian-green">
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                ${isCustom ? `
                                    <input type="text"
                                           id="emergency_title_${index}"
                                           value="${info.title}"
                                           class="font-medium text-gray-900 bg-transparent border-none p-0 focus:ring-0 focus:border-b focus:border-persian-green"
                                           placeholder="Emergency Title">
                                ` : `
                                    <label for="emergency_${index}" class="font-medium text-gray-900 cursor-pointer">
                                        ${info.title}
                                    </label>
                                `}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${sourceClass}">
                                    ${sourceLabel}
                                </span>
                            </div>
                            ${isCustom ? `
                                <button onclick="propertySetupModal.removeCustomEmergencyItem(${index})"
                                        class="text-red-600 hover:text-red-800 p-1"
                                        title="Remove custom item">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            ` : ''}
                        </div>

                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
                                <textarea id="emergency_instructions_${index}"
                                          class="w-full p-2 border border-gray-300 rounded-md text-sm resize-none"
                                          rows="3"
                                          placeholder="Enter emergency instructions...">${info.instructions}</textarea>
                            </div>

                            ${info.location !== undefined ? `
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Location/Additional Info</label>
                                    <input type="text"
                                           id="emergency_location_${index}"
                                           class="w-full p-2 border border-gray-300 rounded-md text-sm"
                                           placeholder="Enter location or additional information..."
                                           value="${info.location}">
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    addEmergencyInfoEventListeners() {
        const container = document.getElementById('emergency-info-content');
        if (!container) return;

        // Add event listeners for checkboxes
        const checkboxes = container.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach((checkbox, index) => {
            checkbox.addEventListener('change', () => {
                console.log(`Emergency checkbox ${index} changed to:`, checkbox.checked);
                this.updateEmergencyItemEnabled(index, checkbox.checked);
                this.saveEmergencyInfoDynamically();
            });
        });

        // Add event listeners for text inputs and textareas
        const textInputs = container.querySelectorAll('input[type="text"], textarea');
        textInputs.forEach((input) => {
            input.addEventListener('input', () => {
                console.log('Emergency info text changed:', input.id);
                this.saveEmergencyInfoDynamically();
            });
        });
    }

    updateEmergencyItemEnabled(index, enabled) {
        if (this.currentEmergencyInfo && this.currentEmergencyInfo[index]) {
            this.currentEmergencyInfo[index].enabled = enabled;
            console.log(`Updated emergency item ${index} enabled status:`, enabled);
        }
    }

    saveEmergencyInfoDynamically() {
        // Debounced auto-save
        clearTimeout(this.emergencyInfoAutoSaveTimeout);
        this.emergencyInfoAutoSaveTimeout = setTimeout(() => {
            console.log('Auto-saving emergency info...');

            // Update current emergency info with form data
            this.updateCurrentEmergencyInfoFromForm();

            // Save to setupData and server
            this.setupData.emergencyInfo = this.currentEmergencyInfo || [];
            this.saveStepToServer(3, { emergencyInfo: this.setupData.emergencyInfo }).catch(error => {
                console.error('Error auto-saving emergency info to server:', error);
            });
        }, 1000);
    }

    updateCurrentEmergencyInfoFromForm() {
        if (!this.currentEmergencyInfo) return;

        const container = document.getElementById('emergency-info-content');
        if (!container) return;

        this.currentEmergencyInfo.forEach((item, index) => {
            const checkbox = document.getElementById(`emergency_${index}`);
            const instructionsTextarea = document.getElementById(`emergency_instructions_${index}`);
            const locationInput = document.getElementById(`emergency_location_${index}`);
            const titleInput = document.getElementById(`emergency_title_${index}`);

            if (checkbox) {
                item.enabled = checkbox.checked;
            }
            if (instructionsTextarea) {
                item.instructions = instructionsTextarea.value.trim();
            }
            if (locationInput) {
                item.location = locationInput.value.trim();
            }
            if (titleInput) {
                item.title = titleInput.value.trim();
            }
        });

        console.log('Updated current emergency info from form:', this.currentEmergencyInfo);
    }

    saveEmergencyInformation() {
        console.log('Saving emergency information...');

        // Clear any previous validation errors
        this.clearValidationErrors();

        // Collect all enabled emergency info from the form
        const allEmergencyInfo = [];
        const validationErrors = [];
        const container = document.getElementById('emergency-info-content');

        if (container) {
            const checkboxes = container.querySelectorAll('input[type="checkbox"]');
            console.log('Found checkboxes:', checkboxes.length);
            checkboxes.forEach((checkbox, index) => {
                console.log(`Checkbox ${index}: checked=${checkbox.checked}, id=${checkbox.id}`);
                if (checkbox.checked) {
                    console.log(`Processing enabled checkbox ${index}`);
                }
                if (checkbox.checked) {
                    const instructionsTextarea = document.getElementById(`emergency_instructions_${index}`);
                    const locationInput = document.getElementById(`emergency_location_${index}`);

                    // For custom items, get title from input field; for others, get from label
                    const titleInput = document.getElementById(`emergency_title_${index}`);
                    const titleElement = document.querySelector(`label[for="emergency_${index}"]`);

                    const title = titleInput ? titleInput.value.trim() :
                                 titleElement ? titleElement.textContent.trim() : '';
                    const instructions = instructionsTextarea ? instructionsTextarea.value.trim() : '';

                    // Basic validation: enabled items should have title and instructions
                    if (!title) {
                        validationErrors.push(`Emergency item ${index + 1} is enabled but missing title`);
                        if (titleInput) {
                            this.highlightRequiredField(`emergency_title_${index}`);
                        }
                    } else {
                        if (titleInput) {
                            this.clearFieldHighlight(`emergency_title_${index}`);
                        }
                    }

                    if (!instructions) {
                        validationErrors.push(`"${title || 'Emergency item'}" is enabled but missing instructions`);
                        this.highlightRequiredField(`emergency_instructions_${index}`);
                    } else {
                        this.clearFieldHighlight(`emergency_instructions_${index}`);
                    }

                    // Determine the type based on the original data
                    const originalItem = this.currentEmergencyInfo && this.currentEmergencyInfo[index];
                    const itemType = originalItem ? originalItem.type : 'default';

                    const emergencyInfo = {
                        id: checkbox.dataset.emergencyId || `emergency_${index}`,
                        title: title,
                        instructions: instructions,
                        location: locationInput ? locationInput.value.trim() : '',
                        enabled: true,
                        type: itemType
                    };

                    allEmergencyInfo.push(emergencyInfo);
                }
            });
        }

        // Show validation errors if any
        if (validationErrors.length > 0) {
            this.showValidationErrors(validationErrors);
            return Promise.resolve(false);
        }

        // Save to setup data and preserve in current session
        this.setupData.emergencyInfo = allEmergencyInfo;
        this.currentEmergencyInfo = allEmergencyInfo; // Preserve for navigation

        console.log('Emergency information to save:', allEmergencyInfo);

        // Save to server
        return this.saveStepToServer(3, { emergencyInfo: allEmergencyInfo });
    }

    addCustomEmergencyItem() {
        console.log('Adding custom emergency item...');

        // Create a new custom emergency item
        const newCustomItem = {
            id: `custom_${Date.now()}`,
            title: '',
            instructions: '',
            location: '',
            enabled: true,
            type: 'custom'
        };

        // Add to current emergency info
        if (!this.currentEmergencyInfo) {
            this.currentEmergencyInfo = [];
        }
        this.currentEmergencyInfo.push(newCustomItem);

        // Re-render the emergency information
        this.renderEmergencyInformation(this.currentEmergencyInfo);

        // Focus on the new item's title field
        const newIndex = this.currentEmergencyInfo.length - 1;
        setTimeout(() => {
            const titleInput = document.getElementById(`emergency_title_${newIndex}`);
            if (titleInput) {
                titleInput.focus();
                titleInput.select();
            }
        }, 100);
    }

    removeCustomEmergencyItem(index) {
        console.log(`Removing custom emergency item at index ${index}...`);

        if (!this.currentEmergencyInfo || index < 0 || index >= this.currentEmergencyInfo.length) {
            console.error('Invalid index for removing emergency item');
            return;
        }

        const item = this.currentEmergencyInfo[index];

        // Only allow removal of custom items
        if (item.type !== 'custom') {
            console.error('Can only remove custom emergency items');
            return;
        }

        // Confirm removal if the item has content
        if (item.title.trim() || item.instructions.trim()) {
            if (!confirm(`Are you sure you want to remove "${item.title || 'this custom emergency item'}"?`)) {
                return;
            }
        }

        // Remove the item
        this.currentEmergencyInfo.splice(index, 1);

        // Re-render the emergency information
        this.renderEmergencyInformation(this.currentEmergencyInfo);
    }



    // House Rules management methods
    getDefaultRulesData() {
        return [
            {
                id: 'smoking',
                title: 'Smoking',
                content: 'No smoking anywhere on the property',
                enabled: false,
                type: 'default'
            },
            {
                id: 'parties',
                title: 'Parties and Events',
                content: 'No parties or events permitted',
                enabled: false,
                type: 'default'
            },
            {
                id: 'quiet_hours',
                title: 'Quiet Hours',
                content: 'Keep noise to a minimum between 10 PM and 8 AM',
                enabled: false,
                type: 'default'
            },
            {
                id: 'pets',
                title: 'Pets',
                content: 'No pets allowed unless specifically approved',
                enabled: false,
                type: 'default'
            },
            {
                id: 'occupancy',
                title: 'Property Capacity',
                content: 'Respect the maximum occupancy limit',
                enabled: false,
                type: 'default'
            },
            {
                id: 'commercial_photography',
                title: 'Commercial Photography',
                content: 'No commercial photography or filming without prior approval',
                enabled: false,
                type: 'default'
            },
            {
                id: 'shoes',
                title: 'Shoes in Property',
                content: 'Remove shoes when entering the property',
                enabled: false,
                type: 'default'
            }
        ];
    }

    adjustDefaultRulesWithImported(defaultRules) {
        // Get imported rules to check for conflicts/updates
        const extractedRules = this.propertyData.importData?.rawData?.extracted?.house_rules || [];
        const deepExtractedRules = this.propertyData.importData?.rawData?.house_rules || [];
        const allImportedRules = [...extractedRules, ...deepExtractedRules];

        console.log('🔧 Adjusting default rules with imported rules:', allImportedRules);

        // First, extract and apply check-in/check-out times to property data
        this.extractAndApplyTimesFromRules(allImportedRules);

        // Then check each imported rule against default rules for replacement
        allImportedRules.forEach(importedRule => {
            const description = importedRule.description?.toLowerCase() || '';
            const originalDescription = importedRule.description || '';

            // Check for quiet hours with specific times
            if (description.includes('quiet') && (description.includes('am') || description.includes('pm') || description.includes(':'))) {
                const quietRule = defaultRules.find(rule => rule.id === 'quiet_hours');
                if (quietRule) {
                    quietRule.description = originalDescription;
                    quietRule.title = importedRule.title || 'Quiet hours';
                    console.log('✅ Updated quiet hours rule with imported content:', quietRule.description);
                }
            }

            // Check for check-in times - update rule but time already extracted to property
            else if (description.includes('check') && description.includes('in') &&
                     (description.includes('am') || description.includes('pm') || description.includes(':'))) {
                const checkinRule = defaultRules.find(rule => rule.id === 'check_in_time');
                if (checkinRule) {
                    checkinRule.description = originalDescription;
                    checkinRule.title = importedRule.title || 'Check-in time';
                    console.log('✅ Updated check-in rule with imported content:', checkinRule.description);
                }
            }

            // Check for check-out times - update rule but time already extracted to property
            else if (description.includes('check') && description.includes('out') &&
                     (description.includes('am') || description.includes('pm') || description.includes(':'))) {
                const checkoutRule = defaultRules.find(rule => rule.id === 'check_out_time');
                if (checkoutRule) {
                    checkoutRule.description = originalDescription;
                    checkoutRule.title = importedRule.title || 'Check-out time';
                    console.log('✅ Updated check-out rule with imported content:', checkoutRule.description);
                }
            }

            // Check for guest limits
            else if (description.includes('guest') && (description.includes('maximum') || description.includes('max') || /\d+/.test(description))) {
                const guestRule = defaultRules.find(rule => rule.id === 'max_guests');
                if (guestRule) {
                    guestRule.description = originalDescription;
                    guestRule.title = importedRule.title || 'Maximum guests';
                    console.log('✅ Updated guest limit rule with imported content:', guestRule.description);
                }
            }

            // Check for smoking rules
            else if (description.includes('smok') || description.includes('no smoking')) {
                const smokingRule = defaultRules.find(rule => rule.id === 'no_smoking');
                if (smokingRule) {
                    smokingRule.description = originalDescription;
                    smokingRule.title = importedRule.title || 'No smoking';
                    console.log('✅ Updated smoking rule with imported content:', smokingRule.description);
                }
            }

            // Check for party/event rules
            else if (description.includes('part') || description.includes('event') || description.includes('gathering')) {
                const partyRule = defaultRules.find(rule => rule.id === 'no_parties');
                if (partyRule) {
                    partyRule.description = originalDescription;
                    partyRule.title = importedRule.title || 'No parties or events';
                    console.log('✅ Updated party rule with imported content:', partyRule.description);
                }
            }

            // Check for pet rules
            else if (description.includes('pet') || description.includes('animal') || description.includes('dog') || description.includes('cat')) {
                const petRule = defaultRules.find(rule => rule.id === 'no_pets');
                if (petRule) {
                    petRule.description = originalDescription;
                    petRule.title = importedRule.title || 'No pets';
                    console.log('✅ Updated pet rule with imported content:', petRule.description);
                }
            }
        });

        return defaultRules;
    }

    extractAndApplyTimesFromRules(importedRules) {
        console.log('🕐 Extracting check-in/check-out times from imported rules...');

        importedRules.forEach(rule => {
            const description = rule.description?.toLowerCase() || '';
            const originalDescription = rule.description || '';

            // Extract check-in times
            if (description.includes('check') && description.includes('in')) {
                const extractedTime = this.extractTimeFromText(originalDescription);
                if (extractedTime) {
                    console.log(`🕐 Extracted check-in time: ${extractedTime} from rule: "${originalDescription}"`);
                    this.propertyData.checkInTime = extractedTime;

                    // Update the basic info form if it's visible
                    const checkinInput = document.getElementById('checkin-time');
                    if (checkinInput) {
                        checkinInput.value = extractedTime;
                        console.log('✅ Updated check-in time input field');
                    }
                }
            }

            // Extract check-out times
            if (description.includes('check') && description.includes('out')) {
                const extractedTime = this.extractTimeFromText(originalDescription);
                if (extractedTime) {
                    console.log(`🕐 Extracted check-out time: ${extractedTime} from rule: "${originalDescription}"`);
                    this.propertyData.checkOutTime = extractedTime;

                    // Update the basic info form if it's visible
                    const checkoutInput = document.getElementById('checkout-time');
                    if (checkoutInput) {
                        checkoutInput.value = extractedTime;
                        console.log('✅ Updated check-out time input field');
                    }
                }
            }
        });
    }

    extractTimeFromText(text) {
        // Try to extract time in various formats
        const timePatterns = [
            /(\d{1,2}):(\d{2})\s*(AM|PM)/i,           // 3:00 PM, 11:30 AM
            /(\d{1,2})\s*(AM|PM)/i,                   // 3 PM, 11 AM
            /(\d{1,2}):(\d{2})/,                      // 15:00, 23:30 (24-hour)
            /after\s+(\d{1,2}):(\d{2})\s*(AM|PM)/i,   // after 3:00 PM
            /before\s+(\d{1,2}):(\d{2})\s*(AM|PM)/i,  // before 11:00 AM
            /(\d{1,2})\s*:\s*(\d{2})\s*(AM|PM)/i      // 3 : 00 PM (with spaces)
        ];

        for (const pattern of timePatterns) {
            const match = text.match(pattern);
            if (match) {
                let hour = parseInt(match[1]);
                let minute = match[2] ? parseInt(match[2]) : 0;
                const ampm = match[3]?.toUpperCase();

                // Convert to 24-hour format
                if (ampm === 'PM' && hour !== 12) {
                    hour += 12;
                } else if (ampm === 'AM' && hour === 12) {
                    hour = 0;
                }

                // Format as HH:MM
                return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
            }
        }

        return null;
    }

    // Amenity management methods
    addBasicAmenity() {
        if (!this.propertyData.amenities) this.propertyData.amenities = { basic: [], appliances: [] };
        // Add an empty amenity that will show as an input field
        this.propertyData.amenities.basic.push('');
        this.loadAmenitiesSection();

        // Focus on the new input field after a short delay
        setTimeout(() => {
            const inputs = document.querySelectorAll('#basic-amenities input[type="text"]');
            const lastInput = inputs[inputs.length - 1];
            if (lastInput) {
                lastInput.focus();
            }
        }, 100);
    }

    async loadUnifiedRules() {
        console.log('🔧 Loading unified rules section...');

        // Check if we already have saved house rules from user modifications
        if (this.setupData.houseRules && this.setupData.houseRules.length > 0) {
            console.log('✅ Using existing saved house rules:', this.setupData.houseRules.length);
            // Convert saved house rules back to the format expected by renderUnifiedRulesSection
            const savedRules = this.setupData.houseRules.map(rule => ({
                ...rule,
                enabled: true // These are already filtered to enabled rules
            }));

            // We need to reconstruct the full rules list to maintain the UI structure
            // Get the original unified rules and update their enabled status
            await this.loadOriginalUnifiedRules();

            // Update the enabled status based on saved rules
            if (this.unifiedRulesData) {
                this.unifiedRulesData.forEach(rule => {
                    const savedRule = this.setupData.houseRules.find(saved =>
                        saved.title === rule.title || saved.description === rule.description
                    );
                    rule.enabled = !!savedRule;
                    if (savedRule) {
                        // Update with saved modifications
                        rule.title = savedRule.title;
                        rule.description = savedRule.description;
                    }
                });

                this.renderUnifiedRulesSection(this.unifiedRulesData);
                return;
            }
        }

        // If no saved rules, load from original sources
        await this.loadOriginalUnifiedRules();
    }

    async loadOriginalUnifiedRules() {
        console.log('🔧 Loading original unified rules from import data...');

        // Get imported rules from multiple sources
        const extractedRules = this.propertyData.importData?.rawData?.extracted?.house_rules || [];
        const deepExtractedRules = this.propertyData.importData?.rawData?.house_rules || [];

        // Combine and deduplicate rules to prevent duplicates
        const rawCombinedRules = [...extractedRules, ...deepExtractedRules];

        // Process "Before you leave" rules first
        const processedRules = this.processBeforeYouLeaveRules(rawCombinedRules);
        let allImportedRules = this.deduplicateRules(processedRules);

        console.log('🔍 Debugging imported rules:');
        console.log('  - extractedRules:', extractedRules);
        console.log('  - deepExtractedRules:', deepExtractedRules);
        console.log('  - allImportedRules:', allImportedRules);
        console.log('  - propertyData.importData:', this.propertyData.importData);
        console.log('  - propertyData.importData?.rawData:', this.propertyData.importData?.rawData);

        // If no rules found in import data, try to fetch from knowledge items as fallback
        if (allImportedRules.length === 0) {
            console.log('No rules in import data, checking knowledge items...');
            try {
                const response = await fetch(`/api/knowledge-items?propertyId=${this.propertyId}`);
                if (response.ok) {
                    const knowledgeData = await response.json();
                    const ruleItems = knowledgeData.items?.filter(item =>
                        item.type === 'rule' &&
                        item.tags?.includes('imported')
                    ) || [];

                    console.log('Found rule items in knowledge:', ruleItems);

                    // Debug each rule item
                    ruleItems.forEach((item, index) => {
                        console.log(`  Rule ${index + 1}:`, {
                            type: item.type,
                            content: item.content,
                            tags: item.tags
                        });
                    });

                    // Convert knowledge items to rule format
                    const knowledgeRules = ruleItems.map(item => ({
                        title: this.extractRuleTitle(item.content),
                        description: item.content,
                        enabled: true,
                        type: 'rule',
                        source: 'knowledge_items'
                    }));

                    allImportedRules = knowledgeRules;
                }
            } catch (error) {
                console.error('Error fetching knowledge items:', error);
            }
        }

        // Filter out invalid imported rules
        const validImportedRules = this.filterValidRules(allImportedRules);
        console.log(`Filtered to ${validImportedRules.length} valid imported rules`);

        // Get default rules and filter out conflicts
        const defaultRules = this.getDefaultRulesData();
        const filteredDefaultRules = this.filterDefaultRulesForConflicts(defaultRules, validImportedRules);

        console.log(`Using ${filteredDefaultRules.length} default rules (${defaultRules.length - filteredDefaultRules.length} filtered out due to conflicts)`);

        // Combine rules: imported rules first (enabled), then default rules (disabled)
        const combinedRules = [
            ...validImportedRules.map(rule => ({ ...rule, enabled: true, source: 'imported' })),
            ...filteredDefaultRules.map(rule => ({ ...rule, enabled: false, source: 'default' }))
        ];

        console.log(`Total unified rules: ${combinedRules.length}`);

        // Store unified rules data for reuse
        this.unifiedRulesData = combinedRules;

        // Render unified rules section
        this.renderUnifiedRulesSection(combinedRules);
    }

    processBeforeYouLeaveRules(rules) {
        /**
         * Find and concatenate "Before you leave" rules into a single rule
         * This improves AI responses by providing comprehensive checkout instructions
         */
        const beforeYouLeaveRules = [];
        const otherRules = [];

        rules.forEach(rule => {
            if (!rule || !rule.description) {
                otherRules.push(rule);
                return;
            }

            const description = rule.description.toLowerCase();
            const title = (rule.title || '').toLowerCase();

            // Check if this is a "Before you leave" rule
            if (description.includes('before you leave') ||
                description.includes('before leaving') ||
                description.includes('when you leave') ||
                title.includes('before you leave') ||
                title.includes('before leaving')) {
                beforeYouLeaveRules.push(rule);
            } else {
                otherRules.push(rule);
            }
        });

        // If we found "Before you leave" rules, concatenate them
        if (beforeYouLeaveRules.length > 0) {
            console.log(`🔗 Found ${beforeYouLeaveRules.length} "Before you leave" rules, concatenating...`);

            // Extract the actual instructions (remove "Before you leave" prefix)
            const instructions = beforeYouLeaveRules.map(rule => {
                let instruction = rule.description;

                // Remove common prefixes
                instruction = instruction.replace(/^before you leave[,:]\s*/i, '');
                instruction = instruction.replace(/^before leaving[,:]\s*/i, '');
                instruction = instruction.replace(/^when you leave[,:]\s*/i, '');

                // Clean up and ensure proper formatting
                instruction = instruction.trim();
                if (instruction && !instruction.endsWith('.') && !instruction.endsWith(',')) {
                    instruction += '.';
                }

                return instruction;
            }).filter(instruction => instruction.length > 0);

            // Create concatenated rule
            if (instructions.length > 0) {
                const concatenatedRule = {
                    id: 'before_you_leave_combined',
                    title: 'Before you leave',
                    description: `Before you leave, ${instructions.join(' ').replace(/\.\s+/g, ', ').replace(/,$/, '.')}`,
                    content: `Before you leave, ${instructions.join(' ').replace(/\.\s+/g, ', ').replace(/,$/, '.')}`,
                    type: 'imported'
                };

                console.log('✅ Created concatenated "Before you leave" rule:', concatenatedRule.description);
                otherRules.push(concatenatedRule);
            }
        }

        return otherRules;
    }

    deduplicateRules(rules) {
        /**
         * Deduplicate house rules based on description content
         * This prevents the same rule from appearing multiple times
         */
        const seen = new Set();
        const deduplicated = [];

        rules.forEach(rule => {
            if (!rule || !rule.description) return;

            // Create a normalized key for comparison
            const normalizedDescription = rule.description.toLowerCase().trim();

            if (!seen.has(normalizedDescription)) {
                seen.add(normalizedDescription);
                deduplicated.push(rule);
            } else {
                console.log(`🚫 Filtered duplicate rule: "${rule.description}"`);
            }
        });

        console.log(`📋 Deduplicated rules: ${rules.length} → ${deduplicated.length}`);
        return deduplicated;
    }

    filterValidRules(rules) {
        return rules.filter(rule => {
            const description = rule.description?.toLowerCase() || '';

            // Filter out UI elements and invalid rules
            const invalidPatterns = [
                'select check-in date', 'select check-out date', 'select date',
                'self check-in', 'self check-out', 'exceptional check-in experience',
                'rated 5.0 out of 5', 'check-in5.0', 'guests1 guest',
                'show more', 'see more', 'hide', 'close', 'back', 'next'
            ];

            // Filter out check-in/check-out time rules (these are handled in Basic Info step)
            const timeRulePatterns = [
                'check-in after', 'check-in before', 'check-in time', 'check-in is',
                'check-out before', 'check-out by', 'check-out time', 'check-out is',
                'checkin after', 'checkin before', 'checkin time', 'checkin is',
                'checkout before', 'checkout by', 'checkout time', 'checkout is'
            ];

            const isInvalid = invalidPatterns.some(pattern => description.includes(pattern));
            const isTimeRule = timeRulePatterns.some(pattern => description.includes(pattern));
            const isTooShort = description.length < 5;
            const isTooGeneric = ['check-in', 'check-out', 'guests', 'pets', 'smoking'].includes(description);

            if (isTimeRule) {
                console.log(`🚫 Filtering out time rule (handled in Basic Info): "${rule.description}"`);
                return false;
            }

            return !isInvalid && !isTooShort && !isTooGeneric;
        });
    }

    filterDefaultRulesForConflicts(defaultRules, importedRules) {
        return defaultRules.filter(defaultRule => {
            // Check if this default rule conflicts with any imported rule
            const hasConflict = importedRules.some(importedRule => {
                return this.rulesConflict(defaultRule, importedRule);
            });

            return !hasConflict;
        });
    }

    rulesConflict(defaultRule, importedRule) {
        // Use content field for new structure, fallback to description for compatibility
        const defaultContent = (defaultRule.content || defaultRule.description || '').toLowerCase();
        const importedContent = (importedRule.content || importedRule.description || '').toLowerCase();

        // Define conflict patterns based on rule IDs and content
        const conflictMappings = [
            {
                defaultIds: ['pets'],
                patterns: ['pet', 'animal', 'dog', 'cat', 'no pets']
            },
            {
                defaultIds: ['smoking'],
                patterns: ['smok', 'no smoking', 'cigarette']
            },
            {
                defaultIds: ['parties'],
                patterns: ['part', 'event', 'gathering', 'no parties']
            },
            {
                defaultIds: ['quiet_hours'],
                patterns: ['quiet', 'noise', 'silent']
            },
            {
                defaultIds: ['occupancy'],
                patterns: ['guest', 'occupancy', 'maximum', 'capacity']
            },
            {
                defaultIds: ['commercial_photography'],
                patterns: ['commercial', 'photography', 'filming', 'photo', 'video']
            }
        ];

        for (const mapping of conflictMappings) {
            // Check if default rule matches by ID
            const defaultMatches = mapping.defaultIds.includes(defaultRule.id);
            // Check if imported rule matches by content patterns
            const importedMatches = mapping.patterns.some(pattern => importedContent.includes(pattern));

            if (defaultMatches && importedMatches) {
                console.log(`🚫 Conflict detected: Default rule "${defaultRule.title}" conflicts with imported rule "${importedRule.title || importedRule.description}"`);
                return true;
            }
        }

        return false;
    }

    renderUnifiedRulesSection(rules) {
        const container = document.getElementById('house-rules-content');
        if (!container) {
            console.error('House rules content container not found');
            return;
        }

        let html = `
            <div class="space-y-4">
                <div class="text-sm text-gray-600 mb-4">
                    <p>Review and customize your house rules. Rules from your listing are enabled by default.</p>
                </div>
        `;

        rules.forEach((rule, index) => {
            const isImported = rule.source === 'imported';
            const sourceLabel = isImported ? 'From Listing' : 'Common Rule';
            const sourceClass = isImported ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600';

            // Use content field for new structure, fallback to description for compatibility
            const ruleContent = rule.content || rule.description || '';

            html += `
                <div class="border rounded-lg p-4 ${isImported ? 'border-blue-200 bg-blue-50' : 'border-gray-200'}">
                    <div class="flex items-start space-x-3">
                        <input type="checkbox"
                               id="rule_${index}"
                               ${rule.enabled ? 'checked' : ''}
                               onchange="propertySetupModal.toggleRule(${index})"
                               class="mt-1 h-4 w-4 text-persian-green border-gray-300 rounded focus:ring-persian-green">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <label for="rule_${index}" class="font-medium text-gray-900 cursor-pointer">
                                    ${rule.title || 'House Rule'}
                                </label>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${sourceClass}">
                                    ${sourceLabel}
                                </span>
                            </div>
                            <textarea id="rule_content_${index}"
                                      class="w-full p-2 border border-gray-300 rounded-md text-sm resize-none"
                                      rows="2"
                                      onchange="propertySetupModal.updateRuleContent(${index}, this.value)"
                                      placeholder="Enter rule content...">${ruleContent}</textarea>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
            </div>

            <!-- Add Custom Rule Section -->
            <div class="mt-6 pt-4 border-t border-gray-200">
                <div id="custom-rule-form" class="hidden mb-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
                    <h5 class="font-medium text-gray-900 mb-3">Add Custom Rule</h5>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Rule Content</label>
                            <textarea id="custom-rule-content"
                                      class="w-full p-2 border border-gray-300 rounded-md text-sm resize-none"
                                      rows="3"
                                      placeholder="e.g., No loud music after 10 PM"></textarea>
                        </div>
                        <div class="flex space-x-3">
                            <button type="button"
                                    onclick="propertySetupModal.saveCustomRule()"
                                    class="px-4 py-2 bg-persian-green text-white rounded-lg hover:bg-persian-green/90 transition-colors">
                                <i class="fas fa-check mr-2"></i>Save Rule
                            </button>
                            <button type="button"
                                    onclick="propertySetupModal.cancelCustomRule()"
                                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                <i class="fas fa-times mr-2"></i>Cancel
                            </button>
                        </div>
                    </div>
                </div>

                <button type="button"
                        id="add-custom-rule-btn"
                        onclick="propertySetupModal.showCustomRuleForm()"
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-persian-green">
                    <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Custom Rule
                </button>
            </div>
        `;

        container.innerHTML = html;

        // Store rules data for saving
        this.unifiedRulesData = rules;

        // Add dynamic saving event listeners
        this.addHouseRulesEventListeners();

        console.log(`✅ Rendered ${rules.length} unified rules (${rules.filter(r => r.source === 'imported').length} imported, ${rules.filter(r => r.source === 'default').length} default)`);
    }

    addHouseRulesEventListeners() {
        const container = document.getElementById('house-rules-content');
        if (!container) return;

        // Add event listeners for checkboxes
        const checkboxes = container.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach((checkbox, index) => {
            checkbox.addEventListener('change', () => {
                console.log(`House rule checkbox ${index} changed to:`, checkbox.checked);
                this.updateHouseRuleEnabled(index, checkbox.checked);
                this.saveHouseRulesDynamically();
            });
        });

        // Add event listeners for text inputs and textareas
        const textInputs = container.querySelectorAll('input[type="text"], textarea');
        textInputs.forEach((input) => {
            input.addEventListener('input', () => {
                console.log('House rule text changed:', input.id);
                this.saveHouseRulesDynamically();
            });
        });
    }

    updateHouseRuleEnabled(index, enabled) {
        if (this.unifiedRulesData && this.unifiedRulesData[index]) {
            this.unifiedRulesData[index].enabled = enabled;
            console.log(`Updated house rule ${index} enabled status:`, enabled);
        }
    }

    saveHouseRulesDynamically() {
        // Debounced auto-save
        clearTimeout(this.houseRulesAutoSaveTimeout);
        this.houseRulesAutoSaveTimeout = setTimeout(() => {
            console.log('Auto-saving house rules...');

            // Update current rules data with form data
            this.updateCurrentHouseRulesFromForm();

            // Save to setupData and server
            this.setupData.houseRules = this.unifiedRulesData?.filter(rule => rule.enabled) || [];
            this.saveStepToServer(2, { houseRules: this.setupData.houseRules }).catch(error => {
                console.error('Error auto-saving house rules to server:', error);
            });
        }, 1000);
    }

    updateCurrentHouseRulesFromForm() {
        if (!this.unifiedRulesData) return;

        const container = document.getElementById('house-rules-content');
        if (!container) return;

        this.unifiedRulesData.forEach((rule, index) => {
            const checkbox = document.getElementById(`rule_${index}`);
            const contentTextarea = document.getElementById(`rule_content_${index}`);

            if (checkbox) {
                rule.enabled = checkbox.checked;
            }
            if (contentTextarea) {
                // Update both content and description for compatibility
                rule.content = contentTextarea.value.trim();
                rule.description = contentTextarea.value.trim();
            }
        });

        console.log('Updated current house rules from form:', this.unifiedRulesData);
    }

    toggleRule(index) {
        if (this.unifiedRulesData && this.unifiedRulesData[index]) {
            const checkbox = document.getElementById(`rule_${index}`);
            if (checkbox) {
                this.unifiedRulesData[index].enabled = checkbox.checked;
                console.log(`Toggled rule ${index}:`, this.unifiedRulesData[index].enabled);
                this.saveHouseRulesDynamically();
            }
        }
    }

    updateRuleContent(index, content) {
        if (this.unifiedRulesData && this.unifiedRulesData[index]) {
            this.unifiedRulesData[index].content = content;
            this.unifiedRulesData[index].description = content; // For compatibility
            console.log(`Updated rule ${index} content:`, content);
            this.saveHouseRulesDynamically();
        }
    }

    showCustomRuleForm() {
        const form = document.getElementById('custom-rule-form');
        const button = document.getElementById('add-custom-rule-btn');

        if (form && button) {
            form.classList.remove('hidden');
            button.classList.add('hidden');

            // Focus on title input
            const titleInput = document.getElementById('custom-rule-title');
            if (titleInput) {
                titleInput.focus();
            }
        }
    }

    cancelCustomRule() {
        const form = document.getElementById('custom-rule-form');
        const button = document.getElementById('add-custom-rule-btn');

        if (form && button) {
            form.classList.add('hidden');
            button.classList.remove('hidden');

            // Clear form
            const contentInput = document.getElementById('custom-rule-content');
            if (contentInput) contentInput.value = '';
        }
    }

    saveCustomRule() {
        const contentInput = document.getElementById('custom-rule-content');

        if (!contentInput) return;

        const content = contentInput.value.trim();

        // Validation
        if (!content) {
            alert('Please enter rule content');
            contentInput.focus();
            return;
        }

        const customRule = {
            id: `custom_${Date.now()}`,
            title: 'Custom Rule',
            content: content,
            description: content, // For compatibility
            enabled: true,
            type: 'custom',
            source: 'custom'
        };

        console.log('Adding custom rule:', customRule);

        this.unifiedRulesData = this.unifiedRulesData || [];
        this.unifiedRulesData.push(customRule);
        this.renderUnifiedRulesSection(this.unifiedRulesData);

        // Auto-save
        this.saveHouseRulesDynamically();

        // Hide form and show button
        this.cancelCustomRule();
    }

    loadCustomRules() {
        // Get existing custom rules from property data
        const existingCustomRules = this.propertyData.houseRules?.filter(rule => rule.type === 'custom') || [];
        this.renderRulesSection('custom-rules-container', existingCustomRules);
    }

    renderRulesSection(containerId, rules) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = rules.map((rule, index) => `
            <div class="border border-gray-200 rounded-lg p-4 ${rule.enabled ? 'bg-green-50 border-green-200' : 'bg-gray-50'}">
                <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-3 flex-1">
                        <!-- Enable/Disable Toggle -->
                        <div class="flex items-center mt-1">
                            <input type="checkbox"
                                   id="${containerId}_${index}"
                                   ${rule.enabled ? 'checked' : ''}
                                   onchange="propertySetupModal.toggleRule('${containerId}', ${index})"
                                   class="w-4 h-4 text-persian-green border-gray-300 rounded focus:ring-persian-green">
                        </div>

                        <!-- Rule Content -->
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h6 class="font-medium text-gray-900">${rule.title}</h6>
                                ${rule.type === 'imported' ? '<span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">From Listing</span>' : ''}
                                ${rule.type === 'custom' ? '<span class="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded">Custom</span>' : ''}
                            </div>

                            <!-- Editable Description -->
                            <textarea id="${containerId}_desc_${index}"
                                      onchange="propertySetupModal.updateRuleDescription('${containerId}', ${index}, this.value)"
                                      class="w-full text-sm text-gray-600 border border-gray-200 rounded p-2 resize-none"
                                      rows="2"
                                      placeholder="Enter rule description...">${rule.description}</textarea>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center space-x-2 ml-4">
                        ${rule.type === 'custom' ? `
                            <button onclick="propertySetupModal.removeCustomRule(${index})"
                                    class="text-red-500 hover:text-red-700 p-1"
                                    title="Remove rule">
                                <i class="fas fa-trash text-sm"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `).join('');
    }

    toggleRule(containerId, index) {
        // Find the rule in the appropriate section and toggle it
        const checkbox = document.getElementById(`${containerId}_${index}`);
        const isEnabled = checkbox.checked;

        // Update the visual state
        const ruleDiv = checkbox.closest('.border');
        if (isEnabled) {
            ruleDiv.classList.remove('bg-gray-50');
            ruleDiv.classList.add('bg-green-50', 'border-green-200');
        } else {
            ruleDiv.classList.remove('bg-green-50', 'border-green-200');
            ruleDiv.classList.add('bg-gray-50');
        }

        // Update the rule data (will be saved when step is saved)
        this.updateRuleInData(containerId, index, { enabled: isEnabled });
    }

    updateRuleDescription(containerId, index, newDescription) {
        this.updateRuleInData(containerId, index, { description: newDescription });
    }

    updateRuleInData(containerId, index, updates) {
        console.log(`Updating rule in ${containerId} at index ${index}:`, updates);

        // Update the rule data based on the container type
        if (containerId === 'default-rules-container') {
            // Update default rules data
            if (!this.defaultRulesData) {
                this.defaultRulesData = this.getDefaultRulesData();
            }
            if (this.defaultRulesData[index]) {
                Object.assign(this.defaultRulesData[index], updates);
                console.log(`Updated default rule ${index}:`, this.defaultRulesData[index]);
            }
        } else if (containerId === 'imported-rules-container') {
            // Update imported rules data
            if (!this.importedRulesData) {
                this.importedRulesData = [];
            }
            if (this.importedRulesData[index]) {
                Object.assign(this.importedRulesData[index], updates);
                console.log(`Updated imported rule ${index}:`, this.importedRulesData[index]);
            }
        } else if (containerId === 'custom-rules-container') {
            // Custom rules are already managed in propertyData.houseRules
            const customRules = this.propertyData.houseRules?.filter(rule => rule.type === 'custom') || [];
            if (customRules[index]) {
                Object.assign(customRules[index], updates);
                console.log(`Updated custom rule ${index}:`, customRules[index]);
            }
        }
    }

    addCustomRule() {
        const title = prompt('Enter rule title:');
        if (!title) return;

        const description = prompt('Enter rule description:');
        if (!description) return;

        const newRule = {
            id: `custom_${Date.now()}`,
            title: title,
            description: description,
            enabled: true,
            type: 'custom'
        };

        // Add to property data
        if (!this.propertyData.houseRules) {
            this.propertyData.houseRules = [];
        }
        this.propertyData.houseRules.push(newRule);

        // Reload custom rules section
        this.loadCustomRules();
    }

    removeCustomRule(index) {
        if (confirm('Are you sure you want to remove this custom rule?')) {
            const customRules = this.propertyData.houseRules?.filter(rule => rule.type === 'custom') || [];
            if (customRules[index]) {
                // Remove from property data
                const ruleToRemove = customRules[index];
                this.propertyData.houseRules = this.propertyData.houseRules.filter(rule => rule.id !== ruleToRemove.id);

                // Reload custom rules section
                this.loadCustomRules();
            }
        }
    }

    extractRuleTitle(description) {
        // Extract a short title from rule description
        if (!description) return 'Custom Rule';

        const words = description.split(' ').slice(0, 4);
        return words.join(' ') + (description.split(' ').length > 4 ? '...' : '');
    }

    updateBasicAmenity(index, value) {
        if (this.propertyData.amenities?.basic) {
            this.propertyData.amenities.basic[index] = value.trim();

            // Auto-save the changes
            this.saveCurrentStepData();

            // If the value is not empty, re-render to show as text instead of input
            if (value.trim() !== '') {
                this.loadAmenitiesSection();
            }
        }
    }

    removeBasicAmenity(index) {
        if (this.propertyData.amenities?.basic) {
            this.propertyData.amenities.basic.splice(index, 1);
            this.loadAmenitiesSection();
            // Auto-save after removal
            this.saveCurrentStepData();
        }
    }

    addAppliance() {
        if (!this.propertyData.amenities) this.propertyData.amenities = { basic: [], appliances: [] };
        this.propertyData.amenities.appliances.push({
            name: '',
            location: '',
            brand: '',
            model: ''
        });
        this.loadAmenitiesSection();
    }

    updateAppliance(index, field, value) {
        if (this.propertyData.amenities?.appliances && this.propertyData.amenities.appliances[index]) {
            this.propertyData.amenities.appliances[index][field] = value.trim();
            // Auto-save the changes
            this.saveCurrentStepData();
        }
    }

    removeAppliance(index) {
        if (this.propertyData.amenities?.appliances) {
            this.propertyData.amenities.appliances.splice(index, 1);
            this.loadAmenitiesSection();
            // Auto-save after removal
            this.saveCurrentStepData();
        }
    }
}

// Global instance
const propertySetupModal = new PropertySetupModal();

// Export for use in other files
window.propertySetupModal = propertySetupModal;
