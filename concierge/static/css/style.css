/* Main Styles for Guestrix */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.5;
}

/* Utility Classes */
/* Note: Removed .hidden class to avoid conflicts with Tailwind's responsive classes */

/* Tab Navigation Styles */
.tab-nav {
    border-bottom: 1px solid #e5e7eb;
}

.tab-button {
    border-color: transparent;
    color: #6b7280;
    transition: all 0.2s;
}

.tab-button:hover {
    color: #059669;
    border-color: transparent;
}

.tab-button.active {
    color: #059669;
    border-color: #059669;
}

/* Tab Content Styles */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Loading Spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #059669;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f9fafb;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f9fafb;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    border-radius: 10px;
    overflow: hidden;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    font-weight: bold;
    padding: 15px 20px;
    background-color: #4a6fdc;
}

.btn-primary {
    background-color: #4a6fdc;
    border-color: #4a6fdc;
}

.btn-primary:hover {
    background-color: #3a5fc9;
    border-color: #3a5fc9;
}

/* Login Page Styles */
.auth-container {
    max-width: 500px;
    margin: 50px auto;
}

/* Dashboard Styles */
.dashboard-header {
    margin-bottom: 30px;
}

.reservation-card {
    margin-bottom: 20px;
}

.reservation-card .card-body {
    padding: 20px;
}

.reservation-status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-confirmed {
    background-color: #d4edda;
    color: #155724;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

/* Chat Styles */
.chat-input-container {
    display: flex;
    margin-top: 15px;
}

.chat-input-container input {
    flex-grow: 1;
    border-radius: 20px 0 0 20px;
    padding: 10px 15px;
}

.chat-input-container button {
    border-radius: 0 20px 20px 0;
}

/* Knowledge Item Status Badges */
.badge.badge-pending {
    background-color: #fff3cd !important;
    color: #856404 !important;
    padding: 5px 10px !important;
    border-radius: 20px !important;
    font-weight: normal !important;
}

.badge.badge-approved {
    background-color: #d4edda !important;
    color: #155724 !important;
    padding: 5px 10px !important;
    border-radius: 20px !important;
    font-weight: normal !important;
}

.badge.badge-rejected {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    padding: 5px 10px !important;
    border-radius: 20px !important;
    font-weight: normal !important;
}

.badge.badge-error {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    padding: 5px 10px !important;
    border-radius: 20px !important;
    font-weight: normal !important;
}

/* Modal Height Adjustments for Short Screens */
@media (max-height: 930px) {
    /* Ensure modal fits on shorter screens */
    #property-setup-modal .bg-white.rounded-lg.shadow-xl {
        max-height: 85vh !important;
    }

    #setup-step-content {
        max-height: calc(85vh - 180px) !important;
    }
}

@media (max-height: 700px) {
    /* For very short screens */
    #property-setup-modal .bg-white.rounded-lg.shadow-xl {
        max-height: 90vh !important;
    }

    #setup-step-content {
        max-height: calc(90vh - 160px) !important;
    }
}

/* Step Names Responsive Behavior - Fixed CSS */
@media (max-width: 480px) {
    /* Hide step names and show icons on narrow screens */
    .step-progress-text {
        display: none !important;
    }

    .step-progress-icon {
        display: inline-block !important;
    }
}

@media (min-width: 481px) {
    /* Show step names and hide icons on wider screens */
    .step-progress-text {
        display: inline !important;
    }

    .step-progress-icon {
        display: none !important;
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 20px;
    }
}
