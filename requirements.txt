# Web framework - Python 3.8 compatible versions
Flask==2.3.3
Flask-SocketIO==5.3.6
python-socketio==5.11.1
websockets==11.0.3
gunicorn==21.2.0
aiohttp==3.9.1
eventlet==0.33.3
gevent==23.9.1

# Environment and utilities
python-dotenv==1.0.1
requests==2.31.0
icalendar==5.0.11
APScheduler==3.10.4
opuslib==3.0.1
numpy==1.24.3

# Firebase / Google Cloud
firebase-admin==6.4.0
google-cloud-firestore==2.13.1
google-cloud-storage==2.10.0

# AWS
boto3==1.34.34
botocore==1.34.34

# AI/ML
google-generativeai==0.8.3
openai==1.6.1
sentence-transformers==2.2.2
torch==2.1.2
transformers==4.36.2

# Web scraping
selenium==4.16.0
beautifulsoup4==4.12.2
lxml==4.9.3

# Audio processing
pydub==0.25.1
librosa==0.10.1
soundfile==0.12.1

# Telephony
twilio==8.11.0

# Utilities
Pillow==10.1.0
python-magic==0.4.27
phonenumbers==8.13.27
pytz==2023.3
