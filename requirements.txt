# Web framework
Flask==3.0.2
Flask-SocketIO==5.3.6
python-socketio==5.11.1
websockets>=13.0.0,<15.1.0
gunicorn==21.2.0
aiohttp==3.9.1
eventlet==0.33.3
gevent>=25.5.0

# Environment and utilities
python-dotenv==1.0.1
requests==2.31.0
icalendar==5.0.11
APScheduler==3.10.4
opuslib==3.0.1
numpy==1.24.3

# Firebase / Google Cloud
firebase-admin==6.4.0
google-cloud-firestore==2.20.2
google-cloud-storage==2.14.0
google-auth==2.27.0
google-auth-oauthlib==1.2.0
google-auth-httplib2==0.2.0
google-api-python-client==2.116.0

# Google AI SDKs
google-genai>=1.24.0  # Required for ephemeral token support
google-generativeai>=0.3.2  # Legacy SDK for backward compatibility

# Vector embeddings
sentence-transformers
boto3==1.34.34
botocore==1.34.34

# File Processing (for Knowledge Base)
pypdf==5.5.0
python-docx==1.1.0
openpyxl==3.1.2
python-magic==0.4.27

# Web scraping dependencies
beautifulsoup4==4.12.2
selenium==4.15.2
lxml==4.9.3