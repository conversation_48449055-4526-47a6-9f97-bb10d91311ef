<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iCal Validation Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .persian-green { color: #00a693; }
        .focus\:ring-persian-green:focus { --tw-ring-color: #00a693; }
        .focus\:border-persian-green:focus { --tw-border-opacity: 1; border-color: #00a693; }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-2xl font-bold text-center mb-8">iCal Validation & Scroll Test</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6" style="height: 600px; overflow-y: auto;" id="modal-content">
            <!-- Validation Error Area (will be populated by JS) -->
            <div id="error-container"></div>
            
            <!-- Basic Information Form -->
            <div class="space-y-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>
                
                <!-- Property Name -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Property Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="property-name" value="Test Property"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green">
                </div>

                <!-- Property Address -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Property Address <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="property-address" value="123 Test Street"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green">
                </div>

                <!-- iCal URL -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        iCal URL (Reservation Sync) <span class="text-red-500">*</span>
                    </label>
                    <input type="url" id="ical-url" value=""
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green"
                           placeholder="https://www.airbnb.com/calendar/ical/..." required>
                    <p class="text-sm text-gray-500 mt-1">
                        <i class="fas fa-info-circle text-blue-500 mr-1"></i>
                        Add your Airbnb calendar export URL to automatically sync reservations.
                    </p>
                </div>

                <!-- WiFi Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            WiFi Network Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="wifi-network" value="TestWiFi"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            WiFi Password <span class="text-red-500">*</span>
                        </label>
                        <input type="password" id="wifi-password" value="password123"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green">
                    </div>
                </div>

                <!-- Filler content to test scrolling -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900">Additional Information</h3>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Property Description</label>
                        <textarea rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg"
                                  placeholder="Describe your property...">A beautiful test property with all amenities.</textarea>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Check-in Time</label>
                            <input type="time" value="15:00" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Check-out Time</label>
                            <input type="time" value="11:00" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                        </div>
                    </div>
                </div>

                <!-- More filler content -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900">Test Scrolling Area</h3>
                    <p class="text-gray-600">This content is here to test the scroll-to-top functionality when validation errors occur.</p>
                    <div class="bg-gray-100 p-4 rounded-lg">
                        <p class="text-sm text-gray-700">When you click "Test Validation" below, the page should scroll to the top to show any validation errors.</p>
                    </div>
                </div>

                <!-- Test Buttons -->
                <div class="flex gap-4 pt-6">
                    <button onclick="testValidation()" 
                            class="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium">
                        Test Validation (iCal Empty)
                    </button>
                    <button onclick="fillIcalAndTest()" 
                            class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium">
                        Fill iCal & Test
                    </button>
                    <button onclick="clearErrors()" 
                            class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium">
                        Clear Errors
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 class="font-semibold text-blue-800 mb-2">Test Instructions:</h3>
            <ol class="text-sm text-blue-700 list-decimal list-inside space-y-1">
                <li>Scroll down to the bottom of the form</li>
                <li>Click "Test Validation" - should scroll to top and show iCal error</li>
                <li>Click "Fill iCal & Test" - should pass validation</li>
                <li>Clear the iCal field and test again</li>
            </ol>
        </div>
    </div>

    <script>
        function testValidation() {
            // Simulate validation logic
            const icalUrl = document.getElementById('ical-url').value.trim();
            const propertyName = document.getElementById('property-name').value.trim();
            const propertyAddress = document.getElementById('property-address').value.trim();
            const wifiNetwork = document.getElementById('wifi-network').value.trim();
            const wifiPassword = document.getElementById('wifi-password').value.trim();

            const validationErrors = [];

            if (!propertyName) {
                validationErrors.push('Property Name is required');
                highlightField('property-name');
            } else {
                clearHighlight('property-name');
            }

            if (!propertyAddress) {
                validationErrors.push('Property Address is required');
                highlightField('property-address');
            } else {
                clearHighlight('property-address');
            }

            if (!icalUrl) {
                validationErrors.push('iCal URL is required for reservation management');
                highlightField('ical-url');
            } else {
                clearHighlight('ical-url');
            }

            if (!wifiNetwork || !wifiPassword) {
                validationErrors.push('WiFi Network Name and Password are required');
                if (!wifiNetwork) highlightField('wifi-network');
                if (!wifiPassword) highlightField('wifi-password');
            } else {
                clearHighlight('wifi-network');
                clearHighlight('wifi-password');
            }

            if (validationErrors.length > 0) {
                showValidationErrors(validationErrors);
                scrollToTop();
            } else {
                clearErrors();
                alert('✅ All validation passed!');
            }
        }

        function fillIcalAndTest() {
            document.getElementById('ical-url').value = 'https://www.airbnb.com/calendar/ical/12345';
            testValidation();
        }

        function showValidationErrors(errors) {
            clearErrors();

            const errorDiv = document.createElement('div');
            errorDiv.id = 'validation-errors';
            errorDiv.className = 'bg-red-50 border border-red-200 rounded-lg p-4 mb-4';
            errorDiv.innerHTML = `
                <div class="flex items-start space-x-3">
                    <i class="fas fa-exclamation-triangle text-red-600 mt-1"></i>
                    <div>
                        <h4 class="text-sm font-medium text-red-800 mb-1">Please fix the following errors:</h4>
                        <ul class="text-sm text-red-700 list-disc list-inside">
                            ${errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;

            const container = document.getElementById('error-container');
            container.appendChild(errorDiv);
        }

        function clearErrors() {
            const errorDiv = document.getElementById('validation-errors');
            if (errorDiv) {
                errorDiv.remove();
            }
            
            // Clear all field highlights
            ['property-name', 'property-address', 'ical-url', 'wifi-network', 'wifi-password'].forEach(clearHighlight);
        }

        function highlightField(fieldId) {
            const field = document.getElementById(fieldId);
            if (field) {
                field.classList.remove('border-gray-300');
                field.classList.add('border-red-500', 'border-2');
            }
        }

        function clearHighlight(fieldId) {
            const field = document.getElementById(fieldId);
            if (field) {
                field.classList.remove('border-red-500', 'border-2');
                field.classList.add('border-gray-300');
            }
        }

        function scrollToTop() {
            const modalContent = document.getElementById('modal-content');
            if (modalContent) {
                modalContent.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
        }
    </script>
</body>
</html>
