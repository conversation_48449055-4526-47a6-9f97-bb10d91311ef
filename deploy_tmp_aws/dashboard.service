[Unit]
Description=Guestrix Flask Web Dashboard (Dev)
After=network.target

[Service]
User=ec2-user
WorkingDirectory=/app/dashboard
Environment=PATH=/app/dashboard/venv/bin
ExecStart=/app/dashboard/venv/bin/gunicorn -c /app/dashboard/gunicorn.conf.py wsgi:application
Restart=always
StandardOutput=append:/var/log/dashboard.log
StandardError=append:/var/log/dashboard.error.log

[Install]
WantedBy=multi-user.target
