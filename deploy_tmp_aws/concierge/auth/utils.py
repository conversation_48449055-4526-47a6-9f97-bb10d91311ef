import os
from functools import wraps
from flask import session, redirect, url_for, request, g, current_app, flash
# Add Firebase imports
import firebase_admin
from firebase_admin import credentials, auth

# Import Firestore functionality
try:
    # Import Firestore functionality
    from concierge.utils.firestore_client import get_user as get_user_firestore
except ImportError:
    print("ERROR: auth/utils.py - Could not import from utils.firestore_client. Check path.")
    # Define stubs to prevent app from running incorrectly
    def get_user_firestore(uid): return None

# Function to get user from Firestore
def get_user(uid):
    """
    Get user data from Firestore.
    """
    print(f"[DEBUG] auth/utils.py - get_user called for uid: {uid}")

    # Get user from Firestore
    user_data = get_user_firestore(uid)
    if user_data:
        print(f"[DEBUG] auth/utils.py - User {uid} found in Firestore")
        return user_data
    else:
        print(f"[DEBUG] auth/utils.py - User {uid} not found in Firestore")
        return None


# No longer need DynamoDB table reference since we're using Firestore

# --- Authentication Decorator ---
def login_required(f):
    """
    Decorator to ensure the user is logged in via session.
    Populates g.user_id and g.user_role if logged in.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_id = session.get('user_id')
        user_role = session.get('user_role')
        if user_id is None or user_role is None:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login', next=request.url)) # Assumes login route is in 'auth' blueprint
        g.user_id = user_id
        g.user_role = user_role
        return f(*args, **kwargs)
    return decorated_function

# --- Firebase Admin Initialization ---
def initialize_firebase_admin():
    """Initialize Firebase Admin SDK if not already initialized."""
    if not firebase_admin._apps:
        try:
            # Try to use environment variable with JSON credentials
            creds_json_str = os.environ.get('FIREBASE_CREDENTIALS_JSON')
            if creds_json_str:
                print("Initializing Firebase Admin SDK using FIREBASE_CREDENTIALS_JSON...")
                import json
                creds_json = json.loads(creds_json_str)
                cred = credentials.Certificate(creds_json)
            else:
                # Try to use application default credentials
                print("Using application default credentials for Firebase Admin SDK...")
                cred = None  # Will use application default credentials

            firebase_admin.initialize_app(cred)
            print("Firebase Admin SDK initialized successfully.")
            return True
        except Exception as e:
            print(f"Error initializing Firebase Admin SDK: {e}")
            import traceback
            traceback.print_exc()
            return False
    return True

# --- Token Verification Function ---
def verify_token(token):
    """
    Verifies a Firebase ID token and returns user information if valid.
    This is a temporary solution until migration to AWS Cognito is complete.
    """
    try:
        # Initialize Firebase Admin SDK if needed
        if not initialize_firebase_admin():
            print("Firebase Admin SDK initialization failed. Cannot verify token.")
            return None

        # Verify the Firebase ID token with clock skew tolerance
        decoded_token = auth.verify_id_token(token, clock_skew_seconds=30)
        print(f"Firebase token verified successfully for UID: {decoded_token.get('uid')}")
        return decoded_token
    except ValueError as e:
        # Invalid token
        print(f"Invalid token: {e}")
        return None
    except Exception as e:
        print(f"Error verifying token: {e}")
        import traceback
        traceback.print_exc()
        return None

# --- Firebase Token Verification (legacy support) ---
def verify_firebase_token(token):
    """
    Verifies a Firebase token and returns user information if valid.
    This is for legacy support until the system fully transitions to AWS.
    """
    # This is a temporary placeholder until Firebase is fully phased out
    # In a real implementation, this would use firebase_admin.auth.verify_id_token()
    try:
        # Directly use get_user since we're moving away from Firebase auth
        # In production, should properly verify the token
        if not token:
            return None

        # Extract user ID from token for now (simplified approach)
        # In a real implementation, would properly decode and verify the token
        user_id = token.split(':')[-1] if ':' in token else token

        # Get user from either Firestore or DynamoDB to verify existence
        user_data = get_user(user_id)
        if user_data:
            # Return a user info dict similar to what Firebase would return
            return {
                'uid': user_id,
                'email': user_data.get('email', user_data.get('Email', '')),
                'name': user_data.get('displayName', user_data.get('DisplayName', '')),
            }
        return None
    except Exception as e:
        print(f"Error verifying token: {e}")
        return None
