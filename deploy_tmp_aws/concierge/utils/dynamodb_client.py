import os
import boto3
import logging
import uuid
import traceback
from datetime import datetime, timezone, timedelta
from boto3.dynamodb.conditions import Key, Attr
from typing import Dict, List, Optional, Any, Union
from botocore.exceptions import ClientError

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# Initialize global variables
dynamodb_client = None
dynamodb_resource = None
# DEPRECATED: ConciergeTable is no longer used - data migrated to Firestore
# DynamoDB is now only used for conversations and websocket connections
conversations_table = None
conversations_table_name = os.environ.get('CONVERSATIONS_TABLE_NAME', 'Conversations')

def initialize_dynamodb():
    """Initialize the DynamoDB client and resource for conversations only."""
    global dynamodb_client, dynamodb_resource, conversations_table

    if dynamodb_client is None:
        try:
            # Initialize both client and resource interfaces
            dynamodb_client = boto3.client('dynamodb')
            dynamodb_resource = boto3.resource('dynamodb')

            # Initialize conversations table only
            try:
                conversations_table = dynamodb_resource.Table(conversations_table_name)
                logger.info(f"DynamoDB initialized with conversations table: {conversations_table_name}")
            except Exception as conv_err:
                logger.error(f"Failed to initialize conversations table: {conv_err}")
                # Continue even if conversations table initialization fails
                # We'll create it later if needed

            return True
        except Exception as e:
            logger.error(f"Failed to initialize DynamoDB: {e}")
            return False
    return True

def get_conversations_table():
    """Get the DynamoDB conversations table resource."""
    if conversations_table is None:
        initialize_dynamodb()
    return conversations_table

# === DEPRECATED FUNCTIONS ===
# The following functions have been migrated to Firestore and are no longer used.
# They are kept here temporarily for reference during the migration period.

def get_user(uid: str) -> Optional[Dict]:
    """DEPRECATED: Use Firestore for user operations."""
    logger.warning("get_user() is deprecated - use Firestore for user operations")
    return None

def create_user(uid: str, user_data: Dict) -> bool:
    """DEPRECATED: Use Firestore for user operations."""
    logger.warning("create_user() is deprecated - use Firestore for user operations")
    return False

def update_user(uid: str, update_data: Dict) -> bool:
    """DEPRECATED: Use Firestore for user operations."""
    logger.warning("update_user() is deprecated - use Firestore for user operations")
    return False

def get_user_by_phone(phone_number: str) -> Optional[Dict]:
    """DEPRECATED: Use Firestore for user operations."""
    logger.warning("get_user_by_phone() is deprecated - use Firestore for user operations")
    return None

def get_property(property_id: str) -> Optional[Dict]:
    """DEPRECATED: Use Firestore for property operations."""
    logger.warning("get_property() is deprecated - use Firestore for property operations")
    return None

def list_all_properties() -> List[Dict]:
    """DEPRECATED: Use Firestore for property operations."""
    logger.warning("list_all_properties() is deprecated - use Firestore for property operations")
    return []

def list_properties_by_host(host_id: str) -> List[Dict]:
    """DEPRECATED: Use Firestore for property operations."""
    logger.warning("list_properties_by_host() is deprecated - use Firestore for property operations")
    return []

def create_property(property_id: str, property_data: Dict) -> bool:
    """DEPRECATED: Use Firestore for property operations."""
    logger.warning("create_property() is deprecated - use Firestore for property operations")
    return False

def update_property(property_id: str, update_data: Dict) -> bool:
    """DEPRECATED: Use Firestore for property operations."""
    logger.warning("update_property() is deprecated - use Firestore for property operations")
    return False

def delete_property(property_id: str) -> bool:
    """DEPRECATED: Use Firestore for property operations."""
    logger.warning("delete_property() is deprecated - use Firestore for property operations")
    return False

def create_knowledge_source(source_id: str, source_data: Dict) -> bool:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("create_knowledge_source() is deprecated - use Firestore for knowledge operations")
    return False

def list_knowledge_sources(property_id: str) -> List[Dict]:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("list_knowledge_sources() is deprecated - use Firestore for knowledge operations")
    return []

def get_knowledge_source(source_id: str) -> Optional[Dict]:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("get_knowledge_source() is deprecated - use Firestore for knowledge operations")
    return None

def update_knowledge_source(source_id: str, update_data: Dict) -> bool:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("update_knowledge_source() is deprecated - use Firestore for knowledge operations")
    return False

def delete_knowledge_source(source_id: str) -> bool:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("delete_knowledge_source() is deprecated - use Firestore for knowledge operations")
    return False

def create_knowledge_item(item_id: str, item_data: Dict) -> bool:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("create_knowledge_item() is deprecated - use Firestore for knowledge operations")
    return False

def list_knowledge_items_by_source(source_id: str) -> List[Dict]:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("list_knowledge_items_by_source() is deprecated - use Firestore for knowledge operations")
    return []

def list_knowledge_items_by_property(property_id: str) -> List[Dict]:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("list_knowledge_items_by_property() is deprecated - use Firestore for knowledge operations")
    return []

def delete_all_knowledge(property_id: str) -> bool:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("delete_all_knowledge() is deprecated - use Firestore for knowledge operations")
    return False

def create_reservation(reservation_data: Dict) -> Optional[str]:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("create_reservation() is deprecated - use Firestore for reservation operations")
    return None

def get_reservation(reservation_id: str) -> Optional[Dict]:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("get_reservation() is deprecated - use Firestore for reservation operations")
    return None

def list_property_reservations(property_id: str) -> List[Dict]:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("list_property_reservations() is deprecated - use Firestore for reservation operations")
    return []

def list_user_reservations(user_id: str) -> List[Dict]:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("list_user_reservations() is deprecated - use Firestore for reservation operations")
    return []

def list_reservations_by_phone(phone_number: str) -> List[Dict]:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("list_reservations_by_phone() is deprecated - use Firestore for reservation operations")
    return []

def get_user_reservations(user_id: str, phone_number: str = None) -> List[Dict]:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("get_user_reservations() is deprecated - use Firestore for reservation operations")
    return []

def list_active_reservations() -> List[Dict]:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("list_active_reservations() is deprecated - use Firestore for reservation operations")
    return []

def update_reservation_phone(reservation_id: str, phone_number: str) -> bool:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("update_reservation_phone() is deprecated - use Firestore for reservation operations")
    return False

def get_knowledge_item(item_id: str) -> Optional[Dict]:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("get_knowledge_item() is deprecated - use Firestore for knowledge operations")
    return None

def update_knowledge_item_status(item_id: str, status: str, error_message: str = None) -> bool:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("update_knowledge_item_status() is deprecated - use Firestore for knowledge operations")
    return False

def update_knowledge_item(item_id: str, update_data: Dict) -> bool:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("update_knowledge_item() is deprecated - use Firestore for knowledge operations")
    return False

def delete_knowledge_item(item_id: str) -> bool:
    """DEPRECATED: Use Firestore for knowledge operations."""
    logger.warning("delete_knowledge_item() is deprecated - use Firestore for knowledge operations")
    return False

def update_property_knowledge_status(property_id: str, status: str) -> bool:
    """DEPRECATED: Use Firestore for property operations."""
    logger.warning("update_property_knowledge_status() is deprecated - use Firestore for property operations")
    return False

def update_reservation_contacts(reservation_id: str, contacts: List[Dict]) -> bool:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("update_reservation_contacts() is deprecated - use Firestore for reservation operations")
    return False

def scan_all_reservations() -> List[Dict]:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("scan_all_reservations() is deprecated - use Firestore for reservation operations")
    return []

def create_test_reservation_for_guest(phone_number: str, guest_name: str = None) -> Optional[str]:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("create_test_reservation_for_guest() is deprecated - use Firestore for reservation operations")
    return None

def update_reservation(reservation_id: str, update_data: Dict) -> bool:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("update_reservation() is deprecated - use Firestore for reservation operations")
    return False

def delete_reservation(reservation_id: str) -> bool:
    """DEPRECATED: Use Firestore for reservation operations."""
    logger.warning("delete_reservation() is deprecated - use Firestore for reservation operations")
    return False

# === ACTIVE CONVERSATION FUNCTIONS ===
# These functions remain active as conversations are still stored in DynamoDB

def create_conversation_session(property_id: str, user_id: str, guest_name: str = None, reservation_id: str = None, phone_number: str = None, channel: str = 'text_chat') -> str:
    """Create a new conversation session and return the session ID.
    
    Args:
        property_id: The property ID this conversation is for
        user_id: The user ID of the person having the conversation
        guest_name: Optional guest name (if not provided, defaults to 'Guest')
        reservation_id: Optional reservation ID to associate with this conversation
        phone_number: Optional phone number for the guest
        channel: Communication channel (default: 'text_chat')
    
    Returns:
        The conversation ID if successful, None otherwise
    """
    if not initialize_dynamodb():
        return None

    # Generate a unique conversation ID
    conversation_id = str(uuid.uuid4())
    timestamp = datetime.now(timezone.utc).isoformat()

    logger.info(f"Creating conversation session: property_id={property_id}, user_id={user_id}, guest_name={guest_name}, reservation_id={reservation_id}, phone_number={phone_number}")

    # Create the conversation session item
    item = {
        'PK': f"PROPERTY#{property_id}",
        'SK': f"CONVERSATION#{conversation_id}",
        'GSI1PK': f"USER#{user_id}",
        'GSI1SK': timestamp,
        'EntityType': 'CONVERSATION',
        'ConversationId': conversation_id,
        'PropertyId': property_id,
        'UserId': user_id,
        'GuestName': guest_name or 'Guest',
        'StartTime': timestamp,
        'LastUpdateTime': timestamp,
        'Channel': channel,
        'MessageCount': 0,
        'Messages': []  # Will store the most recent messages inline
    }

    # Add reservation ID if provided
    if reservation_id:
        item['ReservationId'] = reservation_id

    # Add phone number if provided
    if phone_number:
        item['GuestPhone'] = phone_number

    try:
        conversations_table = get_conversations_table()
        if conversations_table:
            conversations_table.put_item(Item=item)
            logger.info(f"Created conversation session {conversation_id} for property {property_id}, user {user_id}, guest {item['GuestName']}, reservation {reservation_id}, phone {phone_number}")
            return conversation_id
        else:
            logger.error("Conversations table not available")
            return None
    except Exception as e:
        logger.error(f"Error creating conversation session: {e}")
        return None

def add_message_to_conversation(conversation_id: str, property_id: str, message_data: Dict) -> bool:
    """Add a message to an existing conversation.
    
    Args:
        conversation_id: The ID of the conversation to add the message to
        property_id: The property ID (needed for the DynamoDB key)
        message_data: Dict containing message info:
            - role: 'user' or 'assistant'
            - text: The message text
            - phone_number: Optional phone number for user messages
            - context_used: Optional context used for assistant responses
    
    Returns:
        True if successful, False otherwise
    """
    if not initialize_dynamodb():
        return False

    timestamp = datetime.now(timezone.utc).isoformat()

    # Import Decimal for DynamoDB compatibility
    from decimal import Decimal

    # Create the message item
    message = {
        'role': message_data.get('role'),  # 'user' or 'assistant'
        'text': message_data.get('text'),
        'timestamp': timestamp
    }

    # Add phone number if this is a user message and it's available
    if message.get('role') == 'user' and message_data.get('phone_number'):
        message['phone_number'] = message_data.get('phone_number')

    # Add context used if this is an assistant response
    if message.get('role') == 'assistant' and message_data.get('context_used'):
        # Convert any float values to Decimal for DynamoDB compatibility
        context_used = []
        for item in message_data.get('context_used', []):
            # Create a new dict with Decimal values instead of floats
            processed_item = {}
            for key, value in item.items():
                if isinstance(value, float):
                    processed_item[key] = Decimal(str(value))
                else:
                    processed_item[key] = value
            context_used.append(processed_item)

        message['context_used'] = context_used

    try:
        conversations_table = get_conversations_table()
        if not conversations_table:
            logger.error("Conversations table not available")
            return False

        # Update the conversation with the new message
        response = conversations_table.update_item(
            Key={
                'PK': f"PROPERTY#{property_id}",
                'SK': f"CONVERSATION#{conversation_id}"
            },
            UpdateExpression="SET Messages = list_append(if_not_exists(Messages, :empty_list), :new_message), "
                            "LastUpdateTime = :timestamp, "
                            "MessageCount = if_not_exists(MessageCount, :zero) + :one",
            ExpressionAttributeValues={
                ':empty_list': [],
                ':new_message': [message],
                ':timestamp': timestamp,
                ':zero': 0,
                ':one': 1
            },
            ReturnValues="UPDATED_NEW"
        )

        logger.info(f"Added message to conversation {conversation_id}, new count: {response.get('Attributes', {}).get('MessageCount', 'unknown')}")
        return True
    except Exception as e:
        logger.error(f"Error adding message to conversation {conversation_id}: {e}")
        return False

def get_conversation(conversation_id: str, property_id: str) -> Optional[Dict]:
    """Get a conversation by ID."""
    if not initialize_dynamodb():
        return None

    try:
        conversations_table = get_conversations_table()
        if not conversations_table:
            logger.error("Conversations table not available")
            return None

        response = conversations_table.get_item(
            Key={
                'PK': f"PROPERTY#{property_id}",
                'SK': f"CONVERSATION#{conversation_id}"
            }
        )

        if 'Item' in response:
            return response['Item']

        logger.warning(f"Conversation {conversation_id} not found")
        return None
    except Exception as e:
        logger.error(f"Error getting conversation {conversation_id}: {e}")
        return None

def list_property_conversations(property_id: str, limit: int = 100) -> List[Dict]:
    """List all conversations for a property."""
    if not initialize_dynamodb():
        return []

    try:
        conversations_table = get_conversations_table()
        if not conversations_table:
            logger.error("Conversations table not available")
            return []

        response = conversations_table.query(
            KeyConditionExpression=Key('PK').eq(f"PROPERTY#{property_id}") &
                                  Key('SK').begins_with("CONVERSATION#"),
            Limit=limit,
            ScanIndexForward=False  # Sort by most recent first
        )

        conversations = response.get('Items', [])
        logger.info(f"Retrieved {len(conversations)} conversations for property {property_id}")
        return conversations
    except Exception as e:
        logger.error(f"Error listing conversations for property {property_id}: {e}")
        return []

def list_user_conversations(user_id: str, limit: int = 100) -> List[Dict]:
    """List all conversations for a user using GSI1."""
    if not initialize_dynamodb():
        return []

    try:
        conversations_table = get_conversations_table()
        if not conversations_table:
            logger.error("Conversations table not available")
            return []

        response = conversations_table.query(
            IndexName="GSI1",
            KeyConditionExpression=Key('GSI1PK').eq(f"USER#{user_id}"),
            Limit=limit,
            ScanIndexForward=False  # Sort by most recent first
        )

        conversations = response.get('Items', [])
        logger.info(f"Retrieved {len(conversations)} conversations for user {user_id}")
        return conversations
    except Exception as e:
        logger.error(f"Error listing conversations for user {user_id}: {e}")
        return []

def update_conversation(property_id: str, conversation_id: str, update_data: Dict) -> bool:
    """Update a conversation with new data."""
    if not initialize_dynamodb():
        return False

    try:
        conversations_table = get_conversations_table()
        if not conversations_table:
            logger.error("Conversations table not available")
            return False

        # Build update expression and attribute values
        update_expression_parts = []
        expression_attribute_values = {}

        for key, value in update_data.items():
            update_expression_parts.append(f"{key} = :{key.lower()}")
            expression_attribute_values[f":{key.lower()}"] = value

        # Add LastUpdateTime to the update
        update_expression_parts.append("LastUpdateTime = :last_update_time")
        expression_attribute_values[":last_update_time"] = datetime.now(timezone.utc).isoformat()

        # Build the final update expression
        update_expression = "SET " + ", ".join(update_expression_parts)

        # Update the conversation
        response = conversations_table.update_item(
            Key={
                'PK': f"PROPERTY#{property_id}",
                'SK': f"CONVERSATION#{conversation_id}"
            },
            UpdateExpression=update_expression,
            ExpressionAttributeValues=expression_attribute_values,
            ReturnValues="UPDATED_NEW"
        )

        logger.info(f"Updated conversation {conversation_id} with new data: {update_data.keys()}")
        return True
    except Exception as e:
        logger.error(f"Error updating conversation {conversation_id}: {e}")
        return False

def store_conversation(conversation_data: Dict) -> Optional[str]:
    """Store a conversation in DynamoDB."""
    if not initialize_dynamodb():
        return None

    conversation_id = conversation_data.get('conversation_id')
    if not conversation_id:
        import uuid
        conversation_id = str(uuid.uuid4())

    timestamp = datetime.now(timezone.utc).isoformat()

    item = {
        'PK': f"CONVERSATION#{conversation_id}",
        'SK': f"SESSION#{timestamp}",
        'ConversationId': conversation_id,
        'PropertyId': conversation_data.get('property_id'),
        'UserId': conversation_data.get('user_id'),
        'Messages': conversation_data.get('messages', []),
        'CreatedAt': timestamp,
        'UpdatedAt': timestamp,
        'EntityType': 'CONVERSATION'
    }

    # Add optional fields
    if conversation_data.get('guest_name'):
        item['GuestName'] = conversation_data['guest_name']
    if conversation_data.get('reservation_id'):
        item['ReservationId'] = conversation_data['reservation_id']
    if conversation_data.get('phone_number'):
        item['PhoneNumber'] = conversation_data['phone_number']

    try:
        conversations_table = get_conversations_table()
        if conversations_table:
            conversations_table.put_item(Item=item)
            logger.info(f"Stored conversation {conversation_id}")
            return conversation_id
        else:
            logger.error("Conversations table not available")
        return None
    except Exception as e:
        logger.error(f"Error storing conversation: {e}")
        return None

def list_conversations_by_session(session_id: str) -> List[Dict]:
    """List conversations by session ID."""
    if not initialize_dynamodb():
        return []

    try:
        conversations_table = get_conversations_table()
        if not conversations_table:
            logger.error("Conversations table not available")
            return []

        response = conversations_table.query(
            KeyConditionExpression=Key('PK').eq(f"CONVERSATION#{session_id}")
        )
        return response.get('Items', [])
    except Exception as e:
        logger.error(f"Error listing conversations for session {session_id}: {e}")
        return []

def list_conversations_by_property(property_id: str) -> List[Dict]:
    """List conversations by property ID."""
    if not initialize_dynamodb():
        return []

    try:
        conversations_table = get_conversations_table()
        if not conversations_table:
            logger.error("Conversations table not available")
            return []

        response = conversations_table.scan(
            FilterExpression=Attr('PropertyId').eq(property_id)
        )
        return response.get('Items', [])
    except Exception as e:
        logger.error(f"Error listing conversations for property {property_id}: {e}")
        return []

# Initialize DynamoDB client at module load time
initialize_dynamodb()