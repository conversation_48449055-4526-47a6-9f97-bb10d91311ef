# Authentication System Testing Plan

## Overview
This document outlines comprehensive testing for the new standalone phone login and authentication system.

## Implemented Features
✅ **Standalone Phone Login Route** (`/auth/phone-login`)
✅ **PIN Entry Screen** for permanent users
✅ **Host/Guest Signup Selection** for new users  
✅ **OTP Recovery** option in PIN screens
✅ **Magic Link Integration** with phone login

## Test Scenarios

### 1. Standalone Phone Login Flow

#### 1.1 Phone Entry Screen (`/auth/phone-login`)
**Test Cases:**
- [ ] Access URL directly and see phone input form
- [ ] Submit empty phone number → Error message
- [ ] Submit invalid phone format → Error message  
- [ ] Submit valid US phone (******-123-4567) → Continue to next step
- [ ] Test phone number formatting as user types
- [ ] Test back button to main login

**Expected Flow:**
1. User enters phone number
2. System validates and cleans phone number
3. System queries Firestore for user by phone
4. Route to appropriate next step based on user status

#### 1.2 Permanent User Found + Has PIN
**Test Cases:**
- [ ] Enter phone of permanent user with PIN → Redirect to PIN entry
- [ ] PIN entry screen shows masked phone number
- [ ] Enter correct PIN → Login successful, redirect to dashboard
- [ ] Enter wrong PIN (1 attempt) → Error message, 2 attempts remaining
- [ ] Enter wrong PIN (2 attempts) → Error message, 1 attempt remaining  
- [ ] Enter wrong PIN (3 attempts) → Redirect to OTP recovery
- [ ] Test "Forgot PIN" link → Redirect to OTP recovery

#### 1.3 Permanent User Found + No PIN  
**Test Cases:**
- [ ] Enter phone of permanent user without PIN → Redirect to OTP login
- [ ] OTP screen shows "set PIN" next step message
- [ ] Complete OTP verification → Login + prompt to set PIN
- [ ] Dashboard shows PIN setup reminder

#### 1.4 User Not Found (New User)
**Test Cases:**
- [ ] Enter phone of non-existent user → Redirect to signup choice
- [ ] Signup choice screen shows Host vs Guest options
- [ ] Select Guest → Show magic link prompt 
- [ ] Select Host → Redirect to OTP signup with host benefits
- [ ] Complete host signup → Create permanent host account

### 2. PIN Management

#### 2.1 PIN Entry Screen (`/auth/pin-entry`)
**Test Cases:**
- [ ] Four PIN input boxes display correctly
- [ ] Auto-focus on first input
- [ ] Auto-advance between inputs as user types
- [ ] Backspace moves to previous input
- [ ] Paste 4-digit code fills all inputs
- [ ] Non-digit input is rejected
- [ ] Submit button disabled until 4 digits entered
- [ ] Enter key submits when PIN complete

#### 2.2 PIN Verification
**Test Cases:**
- [ ] Valid PIN → Success, redirect to dashboard
- [ ] Invalid PIN → Error message, track attempts
- [ ] 3 failed attempts → Redirect to OTP recovery
- [ ] Attempts indicator shows progress dots

#### 2.3 OTP Recovery (`/auth/otp-recovery`)  
**Test Cases:**
- [ ] Shows "PIN Recovery Mode" notice
- [ ] Firebase reCAPTCHA displays
- [ ] Send code → OTP input screen appears
- [ ] Valid OTP → Login successful, bypass PIN
- [ ] Invalid OTP → Error message, allow retry
- [ ] Resend code functionality works

### 3. Account Creation Flows

#### 3.1 Signup Choice (`/auth/signup-choice`)
**Test Cases:**
- [ ] Two account type options display with features
- [ ] Guest selection → Magic link prompt screen
- [ ] Host selection → OTP signup screen
- [ ] Keyboard navigation between options works
- [ ] Visual selection feedback works

#### 3.2 Guest Magic Link Prompt
**Test Cases:**
- [ ] Shows guidance to use host's magic link
- [ ] Contact host button opens email client
- [ ] Option to create standalone guest account
- [ ] Back button returns to signup choice

#### 3.3 Host OTP Signup (`/auth/otp-signup`)
**Test Cases:**
- [ ] Shows host benefits and badge
- [ ] Firebase OTP flow works end-to-end
- [ ] Account creation succeeds → New host in Firestore
- [ ] Default PIN set to last 4 digits of phone
- [ ] Property setup flag set for new hosts
- [ ] Redirect to dashboard with setup prompts

### 4. Integration with Magic Links

#### 4.1 Magic Link + Phone Login
**Test Cases:**
- [ ] User with magic link but no session → PIN screen
- [ ] PIN screen has "Phone Login" option that carries magic link token
- [ ] Phone login while having magic link preserves reservation access
- [ ] Successful phone login attaches reservation to permanent account

#### 4.2 Token Preservation
**Test Cases:**
- [ ] Magic link token stored in session during phone login
- [ ] Token passed to backend on successful authentication
- [ ] Reservation properly attached to logged-in user account

### 5. Firebase Integration

#### 5.1 OTP Verification
**Test Cases:**
- [ ] Firebase config loaded from `/api/firebase-config`
- [ ] reCAPTCHA renders and functions
- [ ] SMS codes sent successfully
- [ ] OTP verification works end-to-end
- [ ] Error handling for network issues
- [ ] Rate limiting respected

#### 5.2 Backend Token Verification
**Test Cases:**
- [ ] `/auth/complete-phone-auth` endpoint validates Firebase tokens
- [ ] Invalid tokens rejected with proper error
- [ ] Valid tokens create/update user sessions
- [ ] Proper redirect URLs returned

### 6. Database Operations

#### 6.1 Firestore User Queries
**Test Cases:**
- [ ] `find_user_by_phone()` returns correct user or None
- [ ] `verify_user_pin()` correctly validates PINs
- [ ] `update_user_pin()` successfully updates PINs
- [ ] `has_default_pin()` correctly identifies default PINs
- [ ] `create_user_with_pin()` creates users with proper structure

#### 6.2 User Data Structure
**Test Cases:**
- [ ] New permanent users have all required fields
- [ ] Default PIN set correctly for new users
- [ ] Account type properly set (host/guest)
- [ ] Timestamps recorded correctly
- [ ] Phone numbers stored in consistent format

### 7. Session Management

#### 7.1 Session Creation
**Test Cases:**
- [ ] Successful login creates proper session
- [ ] User ID stored correctly in session
- [ ] User role stored correctly in session  
- [ ] Setup flags stored when needed

#### 7.2 Session Validation
**Test Cases:**
- [ ] Protected routes check session properly
- [ ] Expired sessions redirect to login
- [ ] Session data accessible in dashboard
- [ ] Logout clears session completely

### 8. Error Handling

#### 8.1 Network Errors
**Test Cases:**
- [ ] Firebase unavailable → Graceful error message
- [ ] Firestore unavailable → Graceful error message
- [ ] Slow network → Loading indicators and timeouts

#### 8.2 Input Validation
**Test Cases:**
- [ ] Malformed phone numbers → Clear error messages
- [ ] Invalid PIN formats → Clear error messages
- [ ] Missing required fields → Clear error messages

#### 8.3 Edge Cases
**Test Cases:**
- [ ] Multiple tabs/windows → Consistent behavior
- [ ] Browser refresh during flows → Proper state recovery
- [ ] Invalid session data → Clean error handling

## Manual Testing Checklist

### Pre-Testing Setup
- [ ] Clear browser cache and cookies
- [ ] Ensure test phone numbers are available for SMS
- [ ] Verify Firebase project SMS quota
- [ ] Check Firestore rules allow test operations

### Test Data Preparation
- [ ] Create test user with PIN set
- [ ] Create test user without PIN  
- [ ] Create test magic link
- [ ] Prepare invalid phone numbers for testing

### Cross-Browser Testing
- [ ] Chrome desktop
- [ ] Firefox desktop  
- [ ] Safari desktop
- [ ] Chrome mobile
- [ ] Safari mobile
- [ ] Edge desktop

### Accessibility Testing
- [ ] Screen reader navigation
- [ ] Keyboard-only navigation
- [ ] High contrast mode
- [ ] Focus indicators visible
- [ ] ARIA labels present

## Automated Testing Suggestions

### Unit Tests Needed
- [ ] Phone number validation and cleaning
- [ ] PIN validation logic
- [ ] Firestore query functions
- [ ] Session management functions

### Integration Tests Needed  
- [ ] Complete phone login flow
- [ ] OTP verification end-to-end
- [ ] Magic link + phone login combination
- [ ] Account creation flows

### Performance Tests Needed
- [ ] Database query performance
- [ ] Firebase initialization time
- [ ] Page load times for auth screens

## Security Testing

### Authentication Security
- [ ] PIN brute force protection (3 attempts max)
- [ ] Session token security
- [ ] Phone number enumeration protection
- [ ] Rate limiting on SMS sends

### Input Security
- [ ] SQL injection prevention (Firestore)
- [ ] XSS prevention in error messages
- [ ] CSRF protection on forms
- [ ] Phone number format validation

## Success Criteria

### Functional Requirements
- ✅ All primary user flows work end-to-end
- ✅ Error handling is graceful and user-friendly  
- ✅ Integration with existing magic link system
- ✅ Proper session management and security

### Performance Requirements
- [ ] Phone login complete in < 10 seconds
- [ ] PIN verification response < 2 seconds
- [ ] Database queries complete < 5 seconds
- [ ] Page loads complete < 3 seconds

### User Experience Requirements
- [ ] Clear navigation between screens
- [ ] Helpful error messages
- [ ] Consistent visual design
- [ ] Mobile-responsive layouts
- [ ] Accessible to screen readers

## Deployment Checklist

### Pre-Deployment
- [ ] All tests passing
- [ ] Code review completed
- [ ] Database migrations applied
- [ ] Environment variables set
- [ ] Firebase project configured

### Post-Deployment
- [ ] Smoke test all auth flows in production
- [ ] Monitor error rates and performance
- [ ] Test with real phone numbers
- [ ] Verify analytics tracking

## Rollback Plan

### If Issues Detected
1. [ ] Disable new auth routes via feature flag
2. [ ] Redirect users to original login flow
3. [ ] Investigate and fix issues
4. [ ] Re-enable after validation

### Data Rollback
- [ ] New user records can remain (no breaking changes)
- [ ] Session data will naturally expire
- [ ] No database schema changes to rollback

---

## Notes
- Test with various phone number formats (+1, 1, no country code)
- Verify SMS delivery in different regions if applicable
- Test during peak and off-peak hours for performance
- Consider rate limiting impacts during testing 