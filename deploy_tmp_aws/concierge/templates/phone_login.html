{% extends "base.html" %}

{% block title %}Phone Login - Guestrix{% endblock %}

{% block head %}
<!-- Tailwind CSS -->
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
<link
  rel="stylesheet"
  as="style"
  onload="this.rel='stylesheet'"
  href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800"
/>

<style>
    /* Custom color palette */
    :root {
        --persian-green: #2a9d8f;
        --saffron: #e9c46a;
        --dark-purple: #161032;
        --light-cyan: #e0fbfc;
        --bittersweet: #ee6055;
    }

    /* Custom Tailwind configuration */
    .bg-persian-green { background-color: var(--persian-green); }
    .bg-saffron { background-color: var(--saffron); }
    .bg-dark-purple { background-color: var(--dark-purple); }
    .bg-light-cyan { background-color: var(--light-cyan); }
    .bg-bittersweet { background-color: var(--bittersweet); }

    .text-persian-green { color: var(--persian-green); }
    .text-saffron { color: var(--saffron); }
    .text-dark-purple { color: var(--dark-purple); }
    .text-light-cyan { color: var(--light-cyan); }
    .text-bittersweet { color: var(--bittersweet); }

    /* Override default body styles */
    body {
        font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif !important;
        background-color: var(--light-cyan) !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Login card styles */
    .login-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(42, 157, 143, 0.15);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
    }

    .login-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(42, 157, 143, 0.2);
    }

    /* Form input styles */
    .form-input {
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 12px 16px;
        font-size: 16px;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
        background-color: white;
        color: var(--dark-purple);
    }

    .form-input:focus {
        outline: none;
        border-color: var(--persian-green);
        box-shadow: 0 0 0 3px rgba(42, 157, 143, 0.1);
    }

    /* Button styles */
    .btn-primary-custom {
        background-color: var(--persian-green);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-primary-custom:hover:not(:disabled) {
        background-color: #238a7a;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(42, 157, 143, 0.3);
    }

    .btn-secondary-custom {
        background-color: #f3f4f6;
        color: var(--dark-purple);
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }

    .btn-secondary-custom:hover {
        background-color: #e5e7eb;
        color: var(--dark-purple);
        text-decoration: none;
    }

    /* Form text styles */
    .form-text-custom {
        color: #6b7280;
        font-size: 14px;
        margin-top: 6px;
    }

    /* Hide Bootstrap navbar for login page */
    .navbar {
        display: none !important;
    }

    /* Hide Bootstrap container padding */
    .container.mt-4 {
        margin-top: 0 !important;
        padding: 0 !important;
        max-width: none !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-light-cyan flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Header with Logo -->
        <div class="text-center mb-8">
            <div class="flex items-center justify-center gap-3 mb-4">
                <div class="w-12 h-12">
                    <img src="{{ url_for('static', filename='images/guestrix_logo.svg') }}"
                         alt="Guestrix Logo"
                         class="w-full h-full object-contain" />
                </div>
                <h1 class="text-dark-purple text-3xl font-bold">Guestrix</h1>
            </div>
            <p class="text-dark-purple/70 text-lg">
                {% if is_magic_link %}
                    Login with your phone number
                {% else %}
                    Welcome back! Enter your phone number to continue.
                {% endif %}
            </p>
        </div>

        <!-- Phone Login Card -->
        <div class="login-card">
            <!-- Card Header -->
            <div class="bg-persian-green text-white p-6">
                <h2 class="text-xl font-bold mb-0">Phone Login</h2>
            </div>

            <!-- Card Body -->
            <div class="p-6">
                {% if message %}
                    <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <p class="text-blue-700 text-sm">{{ message }}</p>
                    </div>
                {% endif %}

                <!-- Error/Warning messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="mb-4 p-3 {{ 'bg-red-50 border-red-200' if category == 'error' else 'bg-yellow-50 border-yellow-200' }} border rounded-lg">
                                <p class="{{ 'text-red-700' if category == 'error' else 'text-yellow-700' }} text-sm">{{ message }}</p>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <p class="mb-6 text-dark-purple">
                    {% if is_magic_link %}
                        Enter your phone number to access your existing account. We'll send a verification code to confirm it's you.
                    {% else %}
                        Enter your phone number to receive a verification code and continue.
                    {% endif %}
                </p>

                <form method="POST" action="{{ form_action or url_for('auth.phone_login') }}">
                    {% if token %}
                        <input type="hidden" name="token" value="{{ token }}">
                    {% endif %}
                    
                    <div class="mb-6">
                        <label for="phone_number" class="block text-dark-purple font-semibold mb-2">Phone Number</label>
                        <input
                            type="tel"
                            id="phone_number"
                            name="phone_number"
                            class="form-input w-full"
                            placeholder="+****************"
                            value="{{ phone_number or '' }}"
                            required
                        >
                        <div class="form-text-custom">
                            Enter your phone number with country code (e.g., +1 for US)
                        </div>
                    </div>

                    <!-- Legal Consent Section -->
                    <div class="mb-6 p-4 bg-light-cyan/30 rounded-lg border border-persian-green/20">
                        <label class="flex items-start gap-3 text-sm">
                            <input type="checkbox" name="agree_legal_policies" class="mt-1 rounded border-persian-green/30 text-persian-green focus:ring-persian-green" required>
                            <span class="text-dark-purple/80">
                                I agree to Guestrix's 
                                <a href="https://guestrix.ai/terms.html" target="_blank" class="text-persian-green hover:underline">Terms of Use</a>, 
                                <a href="https://guestrix.ai/privacy.html" target="_blank" class="text-persian-green hover:underline">Privacy Policy</a>, 
                                <a href="https://guestrix.ai/refund.html" target="_blank" class="text-persian-green hover:underline">Refund Policy</a>, 
                                and <a href="https://guestrix.ai/cookie.html" target="_blank" class="text-persian-green hover:underline">Cookie Policy</a>
                            </span>
                        </label>
                    </div>

                    <div class="space-y-3">
                        <button type="submit" class="btn-primary-custom w-full">Continue with Phone Number</button>
                        {% if back_url %}
                            <a href="{{ back_url }}" class="btn-secondary-custom w-full">
                                ← {{ back_text or 'Back' }}
                            </a>
                        {% elif is_magic_link %}
                            <a href="{{ url_for('magic.magic_link_access', token=token) }}" class="btn-secondary-custom w-full">
                                ← Back to PIN Screen
                            </a>
                        {% else %}
                            <a href="{{ url_for('auth.login') }}" class="btn-secondary-custom w-full">
                                ← Back to Main Login
                            </a>
                        {% endif %}
                    </div>
                </form>

                <div class="text-center mt-4">
                    <div class="form-text-custom">Having trouble? Contact your host for assistance.</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Focus the phone input on page load
document.addEventListener('DOMContentLoaded', function() {
    const phoneInput = document.getElementById('phone_number');
    phoneInput.focus();

    let previousValue = '';

    // Format phone number as user types
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
        const currentLength = value.length;
        const previousLength = previousValue.replace(/\D/g, '').length;
        const isDeleting = currentLength < previousLength;
        
        // Only add country code if user is typing (not deleting) and doesn't already have it
        if (!isDeleting && value.length > 0 && !value.startsWith('1')) {
            value = '1' + value;
        }
        
        // Allow complete deletion
        if (value.length === 0) {
            e.target.value = '';
            previousValue = '';
            return;
        }
        
        // Format as +1 (xxx) xxx-xxxx
        let formattedValue = '';
        if (value.length >= 1) {
            formattedValue = '+' + value.charAt(0);
        }
        if (value.length >= 2) {
            formattedValue += ' (' + value.substring(1, 4);
        }
        if (value.length >= 5) {
            formattedValue += ') ' + value.substring(4, 7);
        }
        if (value.length >= 8) {
            formattedValue += '-' + value.substring(7, 11);
        }
        
        e.target.value = formattedValue;
        previousValue = formattedValue;
    });

    // Handle keydown for better deletion experience
    phoneInput.addEventListener('keydown', function(e) {
        // If backspace is pressed and we're at the beginning or only have +1, clear the field
        if (e.key === 'Backspace') {
            const cursorPos = e.target.selectionStart;
            const value = e.target.value;
            
            // If we're trying to delete the country code area or the field only contains +1
            if (value === '+1' || (cursorPos <= 3 && value.startsWith('+1'))) {
                e.preventDefault();
                e.target.value = '';
                previousValue = '';
            }
        }
    });
});
</script>
{% endblock %} 