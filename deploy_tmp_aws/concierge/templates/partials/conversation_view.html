{% macro render_conversation(conversation) %}
{# This macro renders a conversation in detail view #}
<div class="conversation-container">
    <!-- Header Section -->
    <div class="conversation-header mb-4">
        <h4>{{ conversation.guest_name|default('Guest', true) }}</h4>
        <div class="d-flex justify-content-between">
            <div>
                <span class="badge channel-badge {{ conversation.channel }}">
                    {{ conversation.channel|replace('_', ' ')|title }}
                </span>
                <span class="text-muted ms-2">{{ conversation.start_time|replace('T', ' ')|replace('Z', '')|truncate(16, True, '') }}</span>
            </div>
        </div>
    </div>

    <!-- Reservation Info Section -->
    {% if conversation.reservation %}
    <div class="reservation-info">
        <div class="conversation-section-title">Reservation Details</div>
        <div class="row">
            <div class="col-md-6">
                <p class="mb-1"><strong>Guest:</strong> {{ conversation.reservation.guestName|default('N/A', true) }}</p>
                <p class="mb-1"><strong>Phone:</strong> {{ conversation.reservation.guestPhone|default('N/A', true) }}</p>
            </div>
            <div class="col-md-6">
                <p class="mb-1"><strong>Check-in:</strong> 
                    <span class="format-date" data-date="{{ conversation.reservation.checkInDate }}">
                        {{ conversation.reservation.checkInDate|default('N/A', true) }}
                    </span>
                </p>
                <p class="mb-1"><strong>Check-out:</strong> 
                    <span class="format-date" data-date="{{ conversation.reservation.checkOutDate }}">
                        {{ conversation.reservation.checkOutDate|default('N/A', true) }}
                    </span>
                </p>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Summary Section -->
    {% if conversation.summary %}
    <div class="summary-section">
        <div class="conversation-section-title">Summary</div>
        <p class="mb-0">{{ conversation.summary }}</p>
    </div>
    {% endif %}

    <!-- Messages Section -->
    <div class="conversation-section">
        <div class="conversation-section-title">Conversation</div>

        {% if conversation.messages and conversation.messages|length > 0 %}
        <div class="conversation-messages">
            {% for message in conversation.messages %}
                {% set role = message.role|default('unknown', true) %}
                {% set text = message.text|default('', true) %}
                {% set timestamp = message.timestamp|default('', true)|replace('T', ' ')|truncate(16, True, '') %}

                {% set has_tool_call = false %}
                {% if role == 'assistant' and (
                    'I searched for' in text or
                    'I found information' in text or
                    'According to my search' in text
                ) %}
                    {% set has_tool_call = true %}
                {% endif %}

                <div class="d-flex flex-column {% if role == 'user' %}align-items-end{% else %}align-items-start{% endif %}">
                    <div class="message-bubble {{ role }}">
                        {{ text|replace('\n', '<br>')|safe }}

                        {% if has_tool_call %}
                        <div class="tool-call-note">
                            <i class="bi bi-search"></i> Used search to find information
                        </div>
                        {% endif %}
                    </div>
                    <div class="message-time">
                        <span class="message-sender">
                            {% if role == 'user' %}
                                {{ conversation.guest_name|default('Guest', true) }}
                            {% else %}
                                Staycee
                            {% endif %}
                        </span> • {{ timestamp }}
                    </div>
                </div>
            {% endfor %}
        </div>
        {% else %}
        <p class="text-muted">No messages available.</p>
        {% endif %}
    </div>
</div>
{% endmacro %}

{% macro render_conversation_card(conversation) %}
<div class="card conversation-card" data-conversation-id="{{ conversation.id }}" data-bs-toggle="modal" data-bs-target="#conversationModal">
    <div class="card-header d-flex justify-content-between align-items-center {% if conversation.channel == 'voice_call' %}voice-header{% else %}text-header{% endif %}">
        <div>
            <span class="fw-bold text-white">{{ conversation.guest_name|default('Guest', true) }}</span>
            {% if conversation.channel %}
                <span class="badge channel-badge-header {{ conversation.channel }}">
                    {{ conversation.channel|replace('_', ' ')|title }}
                </span>
            {% endif %}
        </div>
        <small class="text-white opacity-75">
            {{ conversation.start_time|replace('T', ' ')|replace('Z', '')|truncate(16, True, '') }}
        </small>
    </div>
    <div class="card-body">
        {% if conversation.reservation %}
            <div class="small text-muted mb-2">
                <i class="bi bi-calendar-event"></i>
                <span class="format-date" data-date="{{ conversation.reservation.checkInDate }}">
                    {{ conversation.reservation.checkInDate|default('N/A', true) }}
                </span>
                to 
                <span class="format-date" data-date="{{ conversation.reservation.checkOutDate }}">
                    {{ conversation.reservation.checkOutDate|default('N/A', true) }}
                </span>
                {% if conversation.reservation.guestPhone %}
                <br><i class="bi bi-telephone"></i> {{ conversation.reservation.guestPhone }}
                {% endif %}
            </div>
        {% endif %}

        <div class="conversation-summary">
            {% if conversation.summary %}
                {{ conversation.summary }}
            {% else %}
                <p class="text-muted">No summary available.</p>
            {% endif %}
        </div>

        <div class="d-flex justify-content-between align-items-center mt-2">
            <span class="badge bg-secondary">{{ conversation.message_count }} messages</span>
            <button class="btn btn-sm btn-outline-primary view-conversation-btn">View Details</button>
        </div>
    </div>
</div>
{% endmacro %}

{% macro conversation_styles() %}
<style>
    /* Global styles for consistent fonts */
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    /* Card Styles */
    .conversation-card {
        transition: all 0.2s ease;
        cursor: pointer;
        border: none;
        border-radius: 12px;
        overflow: hidden;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        box-shadow: 0 2px 5px rgba(0,0,0,0.08);
        margin-bottom: 15px;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    .conversation-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    .conversation-card .card-body {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    .conversation-card .conversation-summary {
        flex: 1;
    }

    /* Card Header Styles */
    .card-header.text-header {
        background-color: #0d6efd;
        color: white;
        border-bottom: none;
        padding: 12px 16px;
    }
    .card-header.voice-header {
        background-color: #dc3545;
        color: white;
        border-bottom: none;
        padding: 12px 16px;
    }
    /* Make sure the card headers stand out */
    .card-header {
        font-weight: 500;
    }

    /* Channel Badge Styles */
    .channel-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
    }
    .channel-badge.text-chat {
        background-color: #e3f2fd;
        color: #0d6efd;
    }
    .channel-badge.voice-call {
        background-color: #f8d7da;
        color: #dc3545;
    }
    /* Channel Badge in Header */
    .channel-badge-header {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        border-radius: 8px;
        margin-left: 8px;
        font-weight: normal;
        background-color: rgba(255, 255, 255, 0.25);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.5);
    }

    /* Summary Styles */
    .conversation-summary {
        max-height: 100px;
        overflow: hidden;
        position: relative;
    }
    .conversation-summary::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 20px;
        background: linear-gradient(transparent, white);
    }

    /* Modal Styles */
    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }

    /* Conversation Section Styles */
    .conversation-section {
        margin-bottom: 20px;
    }
    .conversation-section-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #e9ecef;
        color: #495057;
    }

    /* Summary Section */
    .summary-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        border-left: 4px solid #6c757d;
    }

    /* Reservation Info */
    .reservation-info {
        background-color: #e9f7fe;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        border-left: 4px solid #0d6efd;
    }

    /* Message Bubbles */
    .message-bubble {
        border-radius: 18px;
        padding: 12px 16px;
        margin-bottom: 12px;
        max-width: 80%;
        position: relative;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    .message-bubble.user {
        background-color: #f1f0f0;
        margin-right: auto; /* Changed from margin-left: auto */
        border-bottom-left-radius: 5px; /* Changed from border-bottom-right-radius */
    }
    .message-bubble.assistant {
        background-color: #e3f2fd;
        margin-left: auto; /* Changed from margin-right: auto */
        border-bottom-right-radius: 5px; /* Changed from border-bottom-left-radius */
    }

    /* Message Metadata */
    .message-time {
        font-size: 0.7rem;
        color: #6c757d;
        margin-top: 2px;
    }
    .message-sender {
        font-weight: 600;
        font-size: 0.75rem;
    }

    /* Tool Call Notes */
    .tool-call-note {
        font-size: 0.7rem;
        color: #6c757d;
        font-style: italic;
        margin-top: 5px;
        padding: 3px 6px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border-left: 2px solid #6c757d;
    }

    /* Message alignment */
    .d-flex.flex-column.user {
        align-items: flex-start !important; /* Changed from flex-end */
    }
    .d-flex.flex-column.assistant {
        align-items: flex-end !important; /* Changed from flex-start */
    }
</style>

<script>
// Function to format dates using centralized date utilities
function formatDatesInConversation() {
    // Check if DateUtils is available
    if (typeof window.DateUtils === 'undefined') {
        console.log('DateUtils not loaded yet, will format dates when available');
        return;
    }
    
    const dateElements = document.querySelectorAll('.format-date');
    dateElements.forEach(element => {
        const dateStr = element.getAttribute('data-date');
        if (dateStr && dateStr !== 'N/A' && dateStr !== '') {
            try {
                const formattedDate = window.DateUtils.formatDateForDisplay(dateStr, 'short');
                if (formattedDate !== 'Invalid Date') {
                    element.textContent = formattedDate;
                }
            } catch (error) {
                console.warn('Error formatting date:', dateStr, error);
            }
        }
    });
}

// Format dates when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    formatDatesInConversation();
});

// Also format dates when the conversation modal is opened
document.addEventListener('DOMNodeInserted', function() {
    formatDatesInConversation();
});

// Format dates when DateUtils becomes available
if (typeof window.DateUtils === 'undefined') {
    const checkDateUtils = setInterval(function() {
        if (typeof window.DateUtils !== 'undefined') {
            clearInterval(checkDateUtils);
            formatDatesInConversation();
        }
    }, 100);
}
</script>
{% endmacro %}
