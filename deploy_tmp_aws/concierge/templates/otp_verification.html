{% extends "base.html" %}
{% block title %}Verify Phone Number - Guestrix{% endblock %}

{% block head %}
<!-- Tailwind CSS -->
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
<link
  rel="stylesheet"
  as="style"
  onload="this.rel='stylesheet'"
  href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800"
/>

<style>
    /* Custom color palette */
    :root {
        --persian-green: #2a9d8f;
        --saffron: #e9c46a;
        --dark-purple: #161032;
        --light-cyan: #e0fbfc;
        --bittersweet: #ee6055;
    }

    /* Custom Tailwind configuration */
    .bg-persian-green { background-color: var(--persian-green); }
    .bg-saffron { background-color: var(--saffron); }
    .bg-dark-purple { background-color: var(--dark-purple); }
    .bg-light-cyan { background-color: var(--light-cyan); }
    .bg-bittersweet { background-color: var(--bittersweet); }

    .text-persian-green { color: var(--persian-green); }
    .text-saffron { color: var(--saffron); }
    .text-dark-purple { color: var(--dark-purple); }
    .text-light-cyan { color: var(--light-cyan); }
    .text-bittersweet { color: var(--bittersweet); }

    /* Override default body styles */
    body {
        font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif !important;
        background-color: var(--light-cyan) !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Login card styles */
    .login-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(42, 157, 143, 0.15);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
    }

    .login-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(42, 157, 143, 0.2);
    }

    /* OTP input styles */
    .otp-input {
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 16px;
        font-size: 24px;
        font-weight: 600;
        text-align: center;
        letter-spacing: 0.5em;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
        background-color: white;
        color: var(--dark-purple);
    }

    .otp-input:focus {
        outline: none;
        border-color: var(--persian-green);
        box-shadow: 0 0 0 3px rgba(42, 157, 143, 0.1);
    }

    /* Button styles */
    .btn-primary-custom {
        background-color: var(--persian-green);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-primary-custom:hover:not(:disabled) {
        background-color: #238a7a;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(42, 157, 143, 0.3);
    }

    .btn-primary-custom:disabled {
        background-color: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .btn-secondary-custom {
        background-color: #f3f4f6;
        color: var(--dark-purple);
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }

    .btn-secondary-custom:hover {
        background-color: #e5e7eb;
        color: var(--dark-purple);
        text-decoration: none;
    }

    /* Form text styles */
    .form-text-custom {
        color: #6b7280;
        font-size: 14px;
        margin-top: 6px;
    }

    /* Hide Bootstrap navbar for login page */
    .navbar {
        display: none !important;
    }

    /* Hide Bootstrap container padding */
    .container.mt-4 {
        margin-top: 0 !important;
        padding: 0 !important;
        max-width: none !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-light-cyan flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Header with Logo -->
        <div class="text-center mb-8">
            <div class="flex items-center justify-center gap-3 mb-4">
                <div class="w-12 h-12">
                    <img src="{{ url_for('static', filename='images/guestrix_logo.svg') }}"
                         alt="Guestrix Logo"
                         class="w-full h-full object-contain" />
                </div>
                <h1 class="text-dark-purple text-3xl font-bold">Guestrix</h1>
            </div>
            <p class="text-dark-purple/70 text-lg">Verify your phone number</p>
        </div>

        <!-- OTP Verification Card -->
        <div class="login-card">
            <!-- Card Header -->
            <div class="{% if account_type == 'host' %}bg-saffron text-dark-purple{% else %}bg-saffron text-dark-purple{% endif %} p-6">
                <h2 class="text-xl font-bold mb-0">
                    {% if recovery_mode %}
                        Account Recovery
                    {% elif account_type %}
                        {{ account_type|title }} Verification
                    {% else %}
                        Phone Verification
                    {% endif %}
                </h2>
            </div>

            <!-- Card Body -->
            <div class="p-6">
                <!-- Success Message -->
                <div id="success-message" class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg hidden">
                    <p class="text-green-700 text-sm"></p>
                </div>

                <!-- Error Message -->
                <div id="error-message" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg {% if not error_message %}hidden{% endif %}">
                    <p class="text-red-700 text-sm">{{ error_message or '' }}</p>
                </div>

                {% if is_existing_user %}
                    <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="text-blue-800 font-semibold text-sm mb-1">🔑 Logging into your account</div>
                        <p class="text-blue-700 text-xs">We'll verify your identity and log you in securely.</p>
                    </div>
                {% elif is_new_user %}
                    <div class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div class="text-green-800 font-semibold text-sm mb-1">📱 Creating your account</div>
                        <p class="text-green-700 text-xs">After verification, you'll choose your account type and complete setup.</p>
                    </div>
                {% elif account_type == 'host' %}
                    <div class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div class="text-green-800 font-semibold text-sm mb-1">Creating Host Account</div>
                        <ul class="text-green-700 text-xs list-disc list-inside space-y-1">
                            <li>Manage multiple properties</li>
                            <li>Create guest experiences</li>
                            <li>Access host dashboard</li>
                        </ul>
                    </div>
                {% endif %}

                <p class="mb-6 text-dark-purple">
                    We've sent a 6-digit verification code to:<br>
                    <strong class="text-persian-green">{{ phone_number }}</strong>
                </p>

                <form method="POST" action="{{ form_action or url_for('auth.complete_phone_auth') }}">
                    {% if token %}
                        <input type="hidden" name="token" value="{{ token }}">
                    {% endif %}
                    
                    <div class="mb-6">
                        <label for="otp_code" class="block text-dark-purple font-semibold mb-3">Enter Verification Code</label>
                        <input
                            type="text"
                            id="otp_code"
                            name="otp_code"
                            class="otp-input w-full"
                            placeholder="000000"
                            maxlength="6"
                            pattern="[0-9]{6}"
                            autocomplete="one-time-code"
                            required
                        >
                        <div class="form-text-custom">
                            Enter the 6-digit code sent to your phone.
                        </div>
                    </div>

                    <div class="space-y-3">
                        <button type="submit" class="btn-primary-custom w-full">
                            {% if is_existing_user %}
                                Login to Account
                            {% elif is_new_user %}
                                Verify & Continue
                            {% elif source == 'account_creation' %}
                                Create {{ account_type|title }} Account
                            {% elif source == 'phone_login' or recovery_mode %}
                                Login to Account
                            {% elif next_step == 'set_pin' %}
                                Verify & Set PIN
                            {% else %}
                                Verify & Complete Setup
                            {% endif %}
                        </button>
                        
                        {% if back_url %}
                            <a href="{{ back_url }}" class="btn-secondary-custom w-full">
                                ← {{ back_text or 'Back' }}
                            </a>
                        {% elif is_magic_link %}
                            <a href="{{ url_for('magic.show_phone_login', token=token) }}" class="btn-secondary-custom w-full">
                                ← Back to Phone Login
                            </a>
                        {% else %}
                            <a href="{{ url_for('auth.phone_login') }}" class="btn-secondary-custom w-full">
                                ← Back to Phone Login
                            </a>
                        {% endif %}
                    </div>
                </form>

                <div class="text-center mt-6">
                    <p class="form-text-custom mb-2">Didn't receive the code?</p>
                    <button onclick="resendOTP()" class="text-persian-green hover:text-persian-green/80 font-medium text-sm">
                        Resend Code
                    </button>
                </div>

                <div class="text-center mt-4">
                    <div class="form-text-custom">Having trouble? Contact your host for assistance.</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- PIN Creation Modal (appears after successful OTP verification for new users) -->
<div id="pin-creation-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6 transform transition-all mx-4">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-persian-green/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-key text-persian-green text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-dark-purple">Set Up Your Security PIN</h3>
                <p class="text-sm text-dark-purple/70 mt-2">
                    Create a 4-digit PIN for faster and more secure access to your account
                </p>
            </div>

            <!-- Benefits Section -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h6 class="text-sm font-semibold text-dark-purple mb-3">Why set up a PIN?</h6>
                <ul class="text-xs text-dark-purple/80 space-y-2">
                    <li class="flex items-center gap-2">
                        <i class="fas fa-bolt text-persian-green text-xs"></i>
                        Instant access without waiting for OTP codes
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fas fa-shield-alt text-persian-green text-xs"></i>
                        Enhanced security for your reservations
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fas fa-dollar-sign text-persian-green text-xs"></i>
                        Reduces SMS costs by using PIN authentication
                    </li>
                </ul>
            </div>

            <!-- PIN Creation Form -->
            <div class="space-y-4">
                <div>
                    <label for="setup-pin" class="block text-sm font-medium text-dark-purple mb-2">
                        Create Your 4-Digit PIN
                    </label>
                    <input type="password" 
                           id="setup-pin" 
                           maxlength="4" 
                           pattern="[0-9]{4}"
                           class="w-full px-4 py-3 border border-persian-green/20 rounded-lg text-center text-xl tracking-widest focus:outline-none focus:ring-2 focus:ring-persian-green focus:border-persian-green"
                           placeholder="••••">
                    <p class="text-xs text-dark-purple/60 mt-1">Choose a PIN that's memorable but not easily guessed</p>
                </div>

                <div>
                    <label for="setup-confirm-pin" class="block text-sm font-medium text-dark-purple mb-2">
                        Confirm Your PIN
                    </label>
                    <input type="password" 
                           id="setup-confirm-pin" 
                           maxlength="4" 
                           pattern="[0-9]{4}"
                           class="w-full px-4 py-3 border border-persian-green/20 rounded-lg text-center text-xl tracking-widest focus:outline-none focus:ring-2 focus:ring-persian-green focus:border-persian-green"
                           placeholder="••••">
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-3 pt-6">
                <button type="button" 
                        onclick="skipPinSetup()"
                        class="flex-1 px-4 py-3 border border-persian-green/20 rounded-lg text-dark-purple hover:bg-light-cyan transition-colors text-sm">
                    Skip for Now
                </button>
                <button type="button" 
                        onclick="createUserPin()"
                        id="create-pin-btn"
                        class="flex-1 px-4 py-3 bg-persian-green text-white rounded-lg hover:bg-persian-green/90 transition-colors font-medium text-sm">
                    <span id="create-pin-text">Set Up PIN</span>
                    <i id="create-pin-spinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Firebase SDK -->
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>

<script>
// Firebase configuration
let firebaseConfig = null;
{% if firebase_config %}
    firebaseConfig = {{ firebase_config | tojson | safe }};
{% endif %}

// Global variables
let confirmationResult = null;
let isOTPSent = false;

// Focus the OTP input on page load and send OTP
document.addEventListener('DOMContentLoaded', function() {
    const otpInput = document.getElementById('otp_code');
    otpInput.focus();

    // Initialize Firebase and send OTP
    initializeFirebaseAndSendOTP();
});

// Initialize Firebase and send OTP
async function initializeFirebaseAndSendOTP() {
    try {
        // Get Firebase config if not already available
        if (!firebaseConfig) {
            const response = await fetch('/api/firebase-config');
            firebaseConfig = await response.json();
        }

        // Initialize Firebase
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }

        // Send OTP automatically when page loads
        await sendOTP();
    } catch (error) {
        console.error('Firebase initialization error:', error);
        showError('Failed to initialize verification system. Please try again.');
    }
}

// Send OTP using Firebase
async function sendOTP() {
    const phoneNumber = "{{ phone_number }}";

    if (!phoneNumber) {
        showError('No phone number provided. Please go back and enter your phone number.');
        return;
    }

    try {
        // Configure reCAPTCHA
        if (!window.recaptchaVerifier) {
            window.recaptchaVerifier = new firebase.auth.RecaptchaVerifier('recaptcha-container', {
                'size': 'invisible',
                'callback': function(response) {
                    console.log('reCAPTCHA solved');
                }
            });
        }

        // Send OTP
        confirmationResult = await firebase.auth().signInWithPhoneNumber(phoneNumber, window.recaptchaVerifier);
        isOTPSent = true;
        showSuccess('Verification code sent to ' + phoneNumber);

    } catch (error) {
        console.error('Error sending OTP:', error);
        let errorMessage = 'Failed to send verification code. ';

        if (error.code === 'auth/invalid-phone-number') {
            errorMessage += 'Please check your phone number format.';
        } else if (error.code === 'auth/too-many-requests') {
            errorMessage += 'Too many requests. Please try again later.';
        } else {
            errorMessage += 'Please try again or contact support.';
        }

        showError(errorMessage);
    }
}

// Verify OTP and complete account setup
async function verifyOTP(otpCode) {
    if (!confirmationResult) {
        showError('Please request a new verification code.');
        return false;
    }

    try {
        const result = await confirmationResult.confirm(otpCode);
        const user = result.user;
        const idToken = await user.getIdToken();

        // Determine completion endpoint
        const completionUrl = {% if is_magic_link %}"{{ url_for('magic.complete_verification', token=token) }}"{% else %}"{{ url_for('auth.complete_phone_auth') }}"{% endif %};

        // Send verification result to backend
        const response = await fetch(completionUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                idToken: idToken,
                phoneNumber: user.phoneNumber,
                uid: user.uid
            })
        });

        const data = await response.json();

        if (data.success) {
            // Check if this is a new permanent user who needs to set up a PIN
            if (data.new_permanent_user && data.prompt_pin_creation) {
                showPinCreationPrompt(data.redirect || '/dashboard');
                return true;
            } else {
                showSuccess('Phone number verified successfully! Redirecting...');
                setTimeout(() => {
                    window.location.href = data.redirect || '/dashboard';
                }, 1500);
                return true;
            }
        } else {
            showError(data.error || 'Verification failed. Please try again.');
            return false;
        }

    } catch (error) {
        console.error('Error verifying OTP:', error);
        let errorMessage = 'Invalid verification code. ';

        if (error.code === 'auth/invalid-verification-code') {
            errorMessage += 'Please check the code and try again.';
        } else if (error.code === 'auth/code-expired') {
            errorMessage += 'The code has expired. Please request a new one.';
        } else {
            errorMessage += 'Please try again.';
        }

        showError(errorMessage);
        return false;
    }
}

// Show PIN creation prompt after successful OTP verification
function showPinCreationPrompt(redirectUrl) {
    // Hide current form and show PIN creation section
    const currentForm = document.querySelector('.login-card');
    const pinCreationHTML = `
        <div class="login-card" id="pin-creation-section">
            <!-- Card Header -->
            <div class="bg-persian-green text-white p-6">
                <h2 class="text-xl font-bold mb-0">🔐 Secure Your Account</h2>
            </div>

            <!-- Card Body -->
            <div class="p-6">
                <div class="mb-4 p-3 bg-persian-green/10 border border-persian-green/20 rounded-lg">
                    <div class="flex items-center gap-2 text-persian-green mb-2">
                        <i class="fas fa-check-circle"></i>
                        <span class="font-medium">Phone number verified successfully!</span>
                    </div>
                    <p class="text-sm text-dark-purple">Your account is now active.</p>
                </div>

                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-dark-purple mb-3">Create Your Security PIN</h3>
                    <p class="text-sm text-dark-purple/80 mb-4">
                        Set up a 4-digit PIN for faster, secure access to your account. 
                        This reduces SMS costs and provides instant access to your reservations.
                    </p>
                    
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <h4 class="text-sm font-semibold text-dark-purple mb-2">Benefits of Setting Up a PIN:</h4>
                        <ul class="text-xs text-dark-purple/80 space-y-1">
                            <li class="flex items-center gap-2"><i class="fas fa-zap text-persian-green"></i>Instant access without waiting for SMS codes</li>
                            <li class="flex items-center gap-2"><i class="fas fa-shield-alt text-persian-green"></i>Enhanced security for your reservation data</li>
                            <li class="flex items-center gap-2"><i class="fas fa-dollar-sign text-persian-green"></i>Lower costs by reducing SMS verification</li>
                            <li class="flex items-center gap-2"><i class="fas fa-mobile-alt text-persian-green"></i>Works offline once set up</li>
                        </ul>
                    </div>

                    <div class="pin-input-container flex justify-center gap-2 mb-4">
                        <input type="text" class="pin-digit w-12 h-12 border-2 border-persian-green/20 rounded-lg text-center text-lg font-bold focus:outline-none focus:ring-2 focus:ring-persian-green focus:border-persian-green" maxlength="1" data-index="0" autocomplete="off">
                        <input type="text" class="pin-digit w-12 h-12 border-2 border-persian-green/20 rounded-lg text-center text-lg font-bold focus:outline-none focus:ring-2 focus:ring-persian-green focus:border-persian-green" maxlength="1" data-index="1" autocomplete="off">
                        <input type="text" class="pin-digit w-12 h-12 border-2 border-persian-green/20 rounded-lg text-center text-lg font-bold focus:outline-none focus:ring-2 focus:ring-persian-green focus:border-persian-green" maxlength="1" data-index="2" autocomplete="off">
                        <input type="text" class="pin-digit w-12 h-12 border-2 border-persian-green/20 rounded-lg text-center text-lg font-bold focus:outline-none focus:ring-2 focus:ring-persian-green focus:border-persian-green" maxlength="1" data-index="3" autocomplete="off">
                    </div>

                    <div id="pin-error" class="hidden mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p class="text-red-700 text-sm"></p>
                    </div>

                    <div class="space-y-3">
                        <button type="button" id="create-pin-btn" class="btn-primary-custom w-full" disabled>
                            Create PIN & Continue
                        </button>
                        <button type="button" id="skip-pin-btn" class="text-dark-purple/60 hover:text-dark-purple transition-colors text-sm w-full py-2">
                            Skip for now (you can set this up later)
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Replace current content
    currentForm.parentNode.innerHTML = pinCreationHTML;

    // Set up PIN input handlers
    setupPinCreationHandlers(redirectUrl);
}

// Set up PIN creation input handlers
function setupPinCreationHandlers(redirectUrl) {
    const pinInputs = document.querySelectorAll('.pin-digit');
    const createBtn = document.getElementById('create-pin-btn');
    const skipBtn = document.getElementById('skip-pin-btn');
    const pinError = document.getElementById('pin-error');

    // Focus first input
    pinInputs[0].focus();

    pinInputs.forEach((input, index) => {
        input.addEventListener('input', function(e) {
            const value = e.target.value;
            
            // Only allow digits
            if (!/^\d*$/.test(value)) {
                e.target.value = '';
                return;
            }
            
            // Move to next input if value entered
            if (value && index < pinInputs.length - 1) {
                pinInputs[index + 1].focus();
            }
            
            // Enable/disable create button
            updateCreatePinButton();
        });
        
        input.addEventListener('keydown', function(e) {
            // Move to previous input on backspace
            if (e.key === 'Backspace' && !e.target.value && index > 0) {
                pinInputs[index - 1].focus();
            }
            
            // Submit on Enter if PIN is complete
            if (e.key === 'Enter' && getCurrentPin().length === 4) {
                createPin(redirectUrl);
            }
        });
        
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const paste = e.clipboardData.getData('text');
            const digits = paste.replace(/\D/g, '').slice(0, 4);
            
            // Fill in the digits
            for (let i = 0; i < digits.length && i < pinInputs.length; i++) {
                pinInputs[i].value = digits[i];
            }
            
            updateCreatePinButton();
            
            // Focus next empty input or last input
            const nextEmpty = Array.from(pinInputs).findIndex(input => !input.value);
            if (nextEmpty !== -1) {
                pinInputs[nextEmpty].focus();
            } else {
                pinInputs[pinInputs.length - 1].focus();
            }
        });
    });

    function getCurrentPin() {
        return Array.from(pinInputs).map(input => input.value).join('');
    }
    
    function updateCreatePinButton() {
        const pin = getCurrentPin();
        createBtn.disabled = pin.length !== 4;
    }

    // Create PIN button handler
    createBtn.addEventListener('click', function() {
        createPin(redirectUrl);
    });

    // Skip PIN button handler
    skipBtn.addEventListener('click', function() {
        window.location.href = redirectUrl;
    });

    async function createPin(redirectUrl) {
        const pin = getCurrentPin();
        
        if (pin.length !== 4) {
            showPinError('Please enter a 4-digit PIN');
            return;
        }

        // Validate PIN is not too simple
        if (pin === '0000' || pin === '1111' || pin === '2222' || pin === '3333' || 
            pin === '4444' || pin === '5555' || pin === '6666' || pin === '7777' || 
            pin === '8888' || pin === '9999' || pin === '1234' || pin === '4321') {
            showPinError('Please choose a more secure PIN. Avoid sequences like 1234 or repeated digits.');
            return;
        }

        try {
            createBtn.disabled = true;
            createBtn.textContent = 'Creating PIN...';
            
            const response = await fetch('/api/change-pin', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    newPin: pin
                })
            });

            const data = await response.json();

            if (data.success) {
                showSuccess('PIN created successfully! Redirecting to your dashboard...');
                setTimeout(() => {
                    window.location.href = redirectUrl;
                }, 1500);
            } else {
                showPinError(data.error || 'Failed to create PIN. Please try again.');
                createBtn.disabled = false;
                createBtn.textContent = 'Create PIN & Continue';
            }

        } catch (error) {
            console.error('Error creating PIN:', error);
            showPinError('Failed to create PIN. Please try again.');
            createBtn.disabled = false;
            createBtn.textContent = 'Create PIN & Continue';
        }
    }

    function showPinError(message) {
        const errorText = pinError.querySelector('p');
        if (errorText) {
            errorText.textContent = message;
            pinError.classList.remove('hidden');
        }
    }
}

// Format OTP input - only allow digits
document.getElementById('otp_code').addEventListener('input', function(e) {
    this.value = this.value.replace(/\D/g, '');
    if (this.value.length > 6) {
        this.value = this.value.slice(0, 6);
    }
});

// Handle form submission
document.querySelector('form').addEventListener('submit', async function(e) {
    e.preventDefault();

    const otpCode = document.getElementById('otp_code').value.trim();

    if (!otpCode || otpCode.length !== 6) {
        showError('Please enter a valid 6-digit verification code.');
        return;
    }

    // Disable form while verifying
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = 'Verifying...';

    // Verify OTP with Firebase
    const success = await verifyOTP(otpCode);

    if (!success) {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
});

// Resend OTP functionality
async function resendOTP() {
    const resendBtn = document.querySelector('button[onclick="resendOTP()"]');
    const originalText = resendBtn.textContent;
    resendBtn.disabled = true;
    resendBtn.textContent = 'Sending...';

    confirmationResult = null;
    await sendOTP();

    setTimeout(() => {
        resendBtn.disabled = false;
        resendBtn.textContent = originalText;
    }, 3000);
}

// Utility functions for showing messages
function showError(message) {
    const errorDiv = document.getElementById('error-message');
    const errorText = errorDiv.querySelector('p');
    if (errorDiv && errorText) {
        errorText.textContent = message;
        errorDiv.classList.remove('hidden');
    }
    
    const successDiv = document.getElementById('success-message');
    if (successDiv) {
        successDiv.classList.add('hidden');
    }
}

function showSuccess(message) {
    const successDiv = document.getElementById('success-message');
    const successText = successDiv.querySelector('p');
    if (successDiv && successText) {
        successText.textContent = message;
        successDiv.classList.remove('hidden');
    }
    
    const errorDiv = document.getElementById('error-message');
    if (errorDiv) {
        errorDiv.classList.add('hidden');
    }
}
</script>

<!-- reCAPTCHA container (invisible) -->
<div id="recaptcha-container"></div>
{% endblock %} 