{% extends "base.html" %}
{% block title %}Welcome - Guestrix{% endblock %}

{% block head %}
<!-- Tailwind CSS -->
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
<link
  rel="stylesheet"
  as="style"
  onload="this.rel='stylesheet'"
  href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800"
/>

<style>
    /* Custom color palette */
    :root {
        --persian-green: #2a9d8f;
        --saffron: #e9c46a;
        --dark-purple: #161032;
        --light-cyan: #e0fbfc;
        --bittersweet: #ee6055;
    }

    /* Custom Tailwind configuration */
    .bg-persian-green { background-color: var(--persian-green); }
    .bg-saffron { background-color: var(--saffron); }
    .bg-dark-purple { background-color: var(--dark-purple); }
    .bg-light-cyan { background-color: var(--light-cyan); }
    .bg-bittersweet { background-color: var(--bittersweet); }

    .text-persian-green { color: var(--persian-green); }
    .text-saffron { color: var(--saffron); }
    .text-dark-purple { color: var(--dark-purple); }
    .text-light-cyan { color: var(--light-cyan); }
    .text-bittersweet { color: var(--bittersweet); }

    /* Override default body styles */
    body {
        font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif !important;
        background-color: var(--light-cyan) !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Login card styles */
    .login-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(42, 157, 143, 0.15);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
    }

    .login-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(42, 157, 143, 0.2);
    }

    /* Form input styles */
    .form-input {
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 12px 16px;
        font-size: 16px;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
        background-color: white;
        color: var(--dark-purple);
    }

    .form-input:focus {
        outline: none;
        border-color: var(--persian-green);
        box-shadow: 0 0 0 3px rgba(42, 157, 143, 0.1);
    }

    /* Button styles */
    .btn-primary-custom {
        background-color: var(--persian-green);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-primary-custom:hover:not(:disabled) {
        background-color: #238a7a;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(42, 157, 143, 0.3);
    }

    .btn-secondary-custom {
        background-color: #f3f4f6;
        color: var(--dark-purple);
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }

    .btn-secondary-custom:hover {
        background-color: #e5e7eb;
        color: var(--dark-purple);
        text-decoration: none;
    }

    /* Form text styles */
    .form-text-custom {
        color: #6b7280;
        font-size: 14px;
        margin-top: 6px;
    }

    /* Hide Bootstrap navbar for login page */
    .navbar {
        display: none !important;
    }

    /* Hide Bootstrap container padding */
    .container.mt-4 {
        margin-top: 0 !important;
        padding: 0 !important;
        max-width: none !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-light-cyan flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Header with Logo -->
        <div class="text-center mb-8">
            <div class="flex items-center justify-center gap-3 mb-4">
                <div class="w-12 h-12">
                    <img src="{{ url_for('static', filename='images/guestrix_logo.svg') }}"
                         alt="Guestrix Logo"
                         class="w-full h-full object-contain" />
                </div>
                <h1 class="text-dark-purple text-3xl font-bold">Guestrix</h1>
            </div>
            <p class="text-dark-purple/70 text-lg">🎉 Welcome! Let's personalize your experience</p>
        </div>

        <!-- Welcome Card -->
        <div class="login-card">
            <!-- Card Header -->
            <div class="bg-saffron text-dark-purple p-6">
                <h2 class="text-xl font-bold mb-0">Welcome!</h2>
            </div>

            <!-- Card Body -->
            <div class="p-6">
                <p class="mb-6 text-dark-purple">Access verified successfully! Please provide your name for a personalized experience.</p>

                <form method="POST" action="{{ url_for('magic.save_name', token=token) }}">
                    <div class="mb-4">
                        <label for="guest_name" class="block text-dark-purple font-semibold mb-2">Your Name (Optional)</label>
                        <input
                            type="text"
                            id="guest_name"
                            name="guest_name"
                            class="form-input w-full"
                            placeholder="Enter your name"
                            value="{{ prepopulate_name or guest_name_provided or '' }}"
                            maxlength="100"
                        >
                    </div>

                    <div class="mb-6">
                        <label for="phone_number" class="block text-dark-purple font-semibold mb-2">Phone Number (Optional)</label>
                        <input
                            type="tel"
                            id="phone_number"
                            name="phone_number"
                            class="form-input w-full"
                            placeholder="****** 123 4567"
                            maxlength="20"
                        >
                        <div class="form-text-custom">
                            Providing your phone number will create a permanent account with OTP verification.
                        </div>
                    </div>

                    <div class="space-y-3">
                        <button type="submit" class="btn-primary-custom w-full">Continue</button>
                        <a href="{{ url_for('magic.skip_name', token=token) }}" class="btn-secondary-custom w-full">
                            Skip for Now
                        </a>
                    </div>
                </form>

                <div class="text-center mt-4">
                    <div class="form-text-custom">Having trouble? Contact your host for assistance.</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Focus the name input on page load
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('guest_name');
    nameInput.focus();

    // If there's already a name, select it for easy editing
    if (nameInput.value) {
        nameInput.select();
    }
});

// Allow Enter key to submit the form
document.getElementById('guest_name').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        this.form.submit();
    }
});
</script>
{% endblock %}
