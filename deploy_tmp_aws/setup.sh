#!/bin/bash
# Server-side setup script

set -e  # Exit on error

APP_DIR="/app/dashboard"
BACKUP_DIR="${APP_DIR}_backup_$(date +%Y%m%d_%H%M%S)"

# Function to cleanup on exit
cleanup() {
    echo "Cleaning up temporary files..."
    rm -rf /tmp/flask_deploy
}

# Set trap to cleanup on exit
trap cleanup EXIT

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check service status
check_service() {
    if systemctl is-active --quiet "$1"; then
        echo "Service $1 is running"
        return 0
    else
        echo "Service $1 is not running"
        return 1
    fi
}

# Remove existing app directory if it exists (skip backup to save space)
if [ -d "$APP_DIR" ]; then
    echo "Removing existing application directory..."
    sudo rm -rf "$APP_DIR"
fi

# Ensure app directory exists with proper permissions
sudo mkdir -p "$APP_DIR"
sudo chown -R ec2-user:ec2-user "$APP_DIR"

# Copy all deployment files from temp location
if [ -d "/tmp/flask_deploy" ]; then
    echo "Copying deployment files to app directory..."
    cp -r /tmp/flask_deploy/* "$APP_DIR/"
    chown -R ec2-user:ec2-user "$APP_DIR"
fi

# Update system packages (only if not recently updated)
if [ ! -f /var/log/last_yum_update ] || [ $(find /var/log/last_yum_update -mtime +1) ]; then
    echo "Updating system packages..."
    sudo yum update -y
    sudo touch /var/log/last_yum_update
else
    echo "System packages recently updated, skipping..."
fi

# Install system dependencies (check if already installed)
echo "Installing system dependencies..."

# Install nginx using amazon-linux-extras for Amazon Linux 2
if ! command -v nginx &> /dev/null; then
    echo "Installing nginx via amazon-linux-extras..."
    sudo amazon-linux-extras install -y nginx1
else
    echo "Nginx already installed"
fi

# Install Python 3.9 using Amazon Linux Extras
echo "Installing Python 3.9..."
# Check if Python 3.9 is already available
if command -v python3.9 &> /dev/null; then
    echo "Python 3.9 already available"
elif command -v python3 &> /dev/null && python3 --version | grep -q "3.9"; then
    echo "Python 3.9 already installed as python3"
else
    echo "Installing Python 3.9 via amazon-linux-extras..."
    sudo amazon-linux-extras install -y python3.8 || true
    sudo yum install -y python39 python39-pip || true
fi

# Ensure we have pip for Python 3
if ! command -v pip3 &> /dev/null; then
    echo "Installing pip3..."
    sudo yum install -y python3-pip || curl https://bootstrap.pypa.io/get-pip.py | python3
fi

# Verify Python version
python3 --version

# Install certbot for Let's Encrypt
if ! command -v certbot &> /dev/null; then
    echo "Installing certbot..."
    # Change to home directory to avoid directory issues
    cd ~
    # Install EPEL repository for Amazon Linux 2
    sudo amazon-linux-extras install -y epel || true
    # Install certbot and nginx plugin
    sudo yum install -y certbot python2-certbot-nginx || {
        echo "Certbot yum installation failed, trying pip installation..."
        sudo pip3 install certbot certbot-nginx
    }
else
    echo "Certbot already installed"
fi

# Set up Python virtual environment
echo "Setting up Python virtual environment..."
cd "$APP_DIR"
# Remove old virtual environment if it exists (to handle Python version changes)
if [ -d "venv" ]; then
    echo "Removing old virtual environment..."
    rm -rf "venv"
fi

echo "Creating new virtual environment with Python 3.9..."
/usr/local/bin/python3.9 -m venv "venv"

echo "Installing dependencies..."
source "venv/bin/activate"
pip install --upgrade pip
pip install -r requirements.txt

# Install gunicorn and eventlet if not already installed
pip show gunicorn &> /dev/null || pip install gunicorn
pip show eventlet &> /dev/null || pip install eventlet

# Install specific version of google-cloud-firestore
pip install google-cloud-firestore==2.20.2

# Fix urllib3 compatibility issue with Amazon Linux 2 OpenSSL
echo "Fixing urllib3 compatibility with Amazon Linux 2..."
pip install 'urllib3<2.0'

# Install additional dependencies that may be missing
echo "Installing additional required dependencies..."
pip install google-genai numpy boto3

# Fix import paths for lambda_src references
echo "Fixing import paths..."
find "$APP_DIR" -name "*.py" -type f -exec sed -i 's/from concierge\.lambda_src\.firebase_admin_config import/from concierge.utils.firestore_client import/g' {} \;

# Clear Python cache to avoid import issues
echo "Clearing Python cache..."
find "$APP_DIR" -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find "$APP_DIR" -name "*.pyc" -type f -delete 2>/dev/null || true

# Create credentials symlink for Firebase
echo "Setting up Firebase credentials path..."
cd "$APP_DIR"
ln -sf concierge/credentials credentials 2>/dev/null || true

# Fix Firebase credentials path in .env file for server deployment
echo "Fixing Firebase credentials path in .env file..."
if [ -f "concierge/.env" ]; then
    # Update GOOGLE_APPLICATION_CREDENTIALS to use server path
    sed -i 's|GOOGLE_APPLICATION_CREDENTIALS=/Users/<USER>/Workspace/concierge/concierge/credentials/|GOOGLE_APPLICATION_CREDENTIALS=/app/dashboard/concierge/credentials/|g' concierge/.env
    echo "Updated GOOGLE_APPLICATION_CREDENTIALS path for server deployment"
else
    echo "Warning: .env file not found"
fi

# Create log files with proper permissions
echo "Setting up log files..."
sudo touch /var/log/gunicorn_access.log /var/log/gunicorn_error.log /var/log/dashboard.log /var/log/dashboard.error.log
sudo chown ec2-user:ec2-user /var/log/gunicorn_*.log /var/log/dashboard*.log

# Ensure proper ownership of the entire app directory
sudo chown -R ec2-user:ec2-user "$APP_DIR"

# Set up Nginx and SSL
echo "Setting up Nginx and SSL..."
sudo systemctl enable nginx
# Create a temporary HTTP-only config for certbot
sudo tee /etc/nginx/conf.d/dashboard.conf > /dev/null << 'HTTPCONF'
server {
    server_name dev.guestrix.ai;
    listen 80;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
HTTPCONF

# Test and reload nginx
sudo nginx -t && sudo systemctl reload nginx

# Get SSL certificate using nginx plugin (most reliable method)
echo "Obtaining SSL certificate..."
sudo certbot --nginx -d dev.guestrix.ai --non-interactive --agree-tos --email <EMAIL> || {
    echo "Nginx plugin failed, trying standalone method..."
    sudo systemctl stop nginx
    sudo certbot certonly --standalone -d dev.guestrix.ai --non-interactive --agree-tos --email <EMAIL> || {
        echo "SSL certificate setup failed, continuing with HTTP-only"
        sudo systemctl start nginx
        exit 0
    }
    sudo systemctl start nginx
    # If standalone worked, manually configure nginx for SSL
    if [ -f "/etc/letsencrypt/live/dev.guestrix.ai/fullchain.pem" ]; then
        echo "Manually configuring nginx for SSL..."
        sudo cp "$APP_DIR/dashboard.nginx" /etc/nginx/conf.d/dashboard.conf
        sudo nginx -t && sudo systemctl reload nginx
    fi
}

# Set up automatic certificate renewal
echo "Setting up automatic certificate renewal..."
sudo crontab -l 2>/dev/null | grep -q certbot || echo '0 12 * * * /usr/bin/certbot renew --quiet' | sudo crontab -

# Verify SSL setup
if [ -f "/etc/letsencrypt/live/dev.guestrix.ai/fullchain.pem" ]; then
    echo "SSL certificate successfully configured!"
    echo "Certificate expires: $(sudo certbot certificates 2>/dev/null | grep 'Expiry Date' | head -1 || echo 'Unknown')"
else
    echo "SSL certificate not obtained, running with HTTP-only config"
fi

# Set up systemd service
echo "Setting up systemd service..."
sudo cp "$APP_DIR/dashboard.service" /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable dashboard.service
sudo systemctl restart dashboard.service

# Wait for service to start and verify
echo "Waiting for service to start..."
sleep 5
if sudo systemctl is-active --quiet dashboard.service; then
    echo "✅ Dashboard service is running successfully"
    # Test local connectivity
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 | grep -q "302\|200"; then
        echo "✅ Application is responding correctly"
    else
        echo "⚠️  Application may not be responding correctly"
        sudo journalctl -u dashboard.service --no-pager -n 10
    fi
else
    echo "❌ Dashboard service failed to start"
    sudo systemctl status dashboard.service --no-pager
    sudo journalctl -u dashboard.service --no-pager -n 20
fi

# Perform final cleanup
cleanup

echo "Deployment completed successfully!"
if [ -f "/etc/letsencrypt/live/dev.guestrix.ai/fullchain.pem" ]; then
    echo "Application is available at: https://dev.guestrix.ai"
else
    echo "Application is available at: http://dev.guestrix.ai"
fi
